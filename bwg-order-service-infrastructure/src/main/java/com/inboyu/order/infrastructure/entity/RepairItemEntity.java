package com.inboyu.order.infrastructure.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 报修物品实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_item")
@DynamicInsert
@DynamicUpdate
public class RepairItemEntity extends BaseEntity {

    @Column(name = "item_id")
    private Long itemId; // 物品业务ID

    @Column(name = "kind")
    private String kind; // 物品分类

    @Column(name = "name")
    private String name; // 物品名称

    @Column(name = "icon")
    private String icon; // 物品图标

    @Column(name = "url")
    private String url; // Logo地址

    @Column(name = "remark")
    private String remark; // 故障描述标签

    @Column(name = "sort_order")
    private Integer sortOrder; // 排序

    @Column(name = "is_active")
    private Boolean isActive; // 是否启用
}
