package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairStoreItem;
import com.inboyu.order.infrastructure.entity.RepairStoreItemEntity;

import java.util.List;

/**
 * 项目物品配置转换器接口
 */
public interface RepairProjectItemBuilder {

    /**
     * 将实体转换为领域模型
     */
    RepairStoreItem toRepairProjectItem(RepairStoreItemEntity entity);

    /**
     * 将领域模型转换为实体
     */
    RepairStoreItemEntity toRepairProjectItemEntity(RepairStoreItem repairStoreItem);

    /**
     * 批量转换实体列表为领域模型列表
     */
    List<RepairStoreItem> toRepairProjectItemList(List<RepairStoreItemEntity> entityList);

    /**
     * 批量转换领域模型列表为实体列表
     */
    List<RepairStoreItemEntity> toRepairProjectItemEntityList(List<RepairStoreItem> repairStoreItemList);

    /**
     * 更新实体字段
     */
    RepairStoreItemEntity updateEntity(RepairStoreItemEntity entity, RepairStoreItem repairStoreItem);
}
