package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.CustomerId;
import com.inboyu.order.domain.repair.model.RoomId;
import com.inboyu.order.domain.repair.model.WorkflowId;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;
import com.inboyu.order.domain.repair.model.OrderType;
import com.inboyu.order.domain.repair.model.TagStatus;
import com.inboyu.order.infrastructure.entity.RepairOrderEntity;

import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修工单Builder实现类
 */
@Component
public class RepairOrderBuilderImpl implements RepairOrderBuilder {

    @Override
    public RepairOrder toDomain(RepairOrderEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairOrder.builder()
                .id(entity.getId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion() != null ? entity.getVersion().longValue() : null)
                .deleted(entity.getDeleted())
                .repairOrderId(entity.getRepairOrderId() != null ? RepairOrderId.of(entity.getRepairOrderId()) : null)
                .customerId(entity.getCustomerId() != null ? CustomerId.of(entity.getCustomerId()) : null)
                .roomId(entity.getRoomId() != null ? RoomId.of(entity.getRoomId()) : null)
                .buildingId(entity.getBuildingId())
                .storeId(entity.getStoreId())
                .itemId(entity.getItemId())
                .workflowId(entity.getWorkflowId() != null ? WorkflowId.of(entity.getWorkflowId()) : null)
                .area(entity.getArea())
                .detail(entity.getDetail())
                .contactPhone(entity.getContactPhone())
                .submitter(entity.getSubmitter())
                .willRepairDate(entity.getWillRepairDate())
                .willRepairStart(entity.getWillRepairStart())
                .willRepairEnd(entity.getWillRepairEnd())
                .isEnter(entity.getIsEnter())
                .status(entity.getStatus() != null ? RepairOrderStatus.of(entity.getStatus()) : null)
                .orderType(entity.getOrderType() != null ? OrderType.of(entity.getOrderType()) : null)
                .tagStatus(entity.getTagStatus() != null ? TagStatus.of(entity.getTagStatus()) : null)
                .refuseReason(entity.getRefuseReason())
                .suspendReason(entity.getSuspendReason())
                .preDoorTime(entity.getPreDoorTime())
                .preRepairTime(entity.getPreRepairTime())
                .finishTime(entity.getFinishTime())
                .dealTime(entity.getDealTime())
                .responsibleId(entity.getResponsibleId())
                .maintainerId(entity.getMaintainerId())
                .remark(entity.getRemark())
                .build();
    }

    @Override
    public RepairOrderEntity toEntity(RepairOrder domain) {
        if (domain == null) {
            return null;
        }

        RepairOrderEntity entity = new RepairOrderEntity();
        entity.setId(domain.getId());
        entity.setCreateTime(domain.getCreateTime());
        entity.setModifyTime(domain.getModifyTime());
        entity.setVersion(domain.getVersion() != null ? domain.getVersion().intValue() : null);
        entity.setDeleted(domain.getDeleted());
        entity.setRepairOrderId(domain.getRepairOrderId() != null ? domain.getRepairOrderId().getValue() : null);
        entity.setCustomerId(domain.getCustomerId() != null ? domain.getCustomerId().getValue() : null);
        entity.setRoomId(domain.getRoomId() != null ? domain.getRoomId().getValue() : null);
        entity.setBuildingId(domain.getBuildingId());
        entity.setStoreId(domain.getStoreId());
        entity.setItemId(domain.getItemId());
        entity.setWorkflowId(domain.getWorkflowId() != null ? domain.getWorkflowId().getValue() : null);
        entity.setArea(domain.getArea());
        entity.setDetail(domain.getDetail());
        entity.setContactPhone(domain.getContactPhone());
        entity.setSubmitter(domain.getSubmitter());
        entity.setWillRepairDate(domain.getWillRepairDate());
        entity.setWillRepairStart(domain.getWillRepairStart());
        entity.setWillRepairEnd(domain.getWillRepairEnd());
        entity.setIsEnter(domain.getIsEnter());
        entity.setStatus(domain.getStatus() != null ? domain.getStatus().getCode() : null);
        entity.setOrderType(domain.getOrderType() != null ? domain.getOrderType().getCode() : null);
        entity.setTagStatus(domain.getTagStatus() != null ? domain.getTagStatus().getCode() : null);
        entity.setRefuseReason(domain.getRefuseReason());
        entity.setSuspendReason(domain.getSuspendReason());
        entity.setPreDoorTime(domain.getPreDoorTime());
        entity.setPreRepairTime(domain.getPreRepairTime());
        entity.setFinishTime(domain.getFinishTime());
        entity.setDealTime(domain.getDealTime());
        entity.setResponsibleId(domain.getResponsibleId());
        entity.setMaintainerId(domain.getMaintainerId());
        entity.setRemark(domain.getRemark());

        return entity;
    }

    @Override
    public List<RepairOrder> toDomains(List<RepairOrderEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairOrderEntity> toEntities(List<RepairOrder> domains) {
        if (domains == null) {
            return null;
        }
        return domains.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}