package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairStoreItem;
import com.inboyu.order.domain.repair.model.ProjectId;
import com.inboyu.order.infrastructure.entity.RepairStoreItemEntity;
import com.inboyu.order.infrastructure.builder.RepairProjectItemBuilder;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目物品配置Builder实现
 */
@Component
public class RepairProjectItemBuilderImpl implements RepairProjectItemBuilder {

    @Override
    public RepairStoreItemEntity toRepairProjectItemEntity(RepairStoreItem domainModel) {
        if (domainModel == null) {
            return null;
        }

        RepairStoreItemEntity entity = new RepairStoreItemEntity();
        entity.setId(domainModel.getId());
        entity.setConfigId(domainModel.getConfigId());
        entity.setProjectId(domainModel.getProjectId().getValue());
        entity.setItemId(domainModel.getItemId());
        entity.setSort(domainModel.getSort());
        entity.setCreateTime(domainModel.getCreateTime());
        entity.setModifyTime(domainModel.getModifyTime());
        entity.setVersion(domainModel.getVersion().intValue());
        entity.setDeleted(domainModel.getDeleted());

        return entity;
    }

    @Override
    public RepairStoreItem toRepairProjectItem(RepairStoreItemEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairStoreItem.builder()
                .id(entity.getId())
                .configId(entity.getConfigId())
                .projectId(ProjectId.of(entity.getProjectId()))
                .itemId(entity.getItemId())
                .sort(entity.getSort())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(Long.valueOf(entity.getVersion()))
                .deleted(entity.getDeleted())
                .build();
    }

    @Override
    public RepairStoreItemEntity updateEntity(RepairStoreItemEntity entity, RepairStoreItem domainModel) {
        if (entity == null || domainModel == null) {
            return entity;
        }

        entity.setItemId(domainModel.getItemId());
        entity.setSort(domainModel.getSort());
        entity.setModifyTime(LocalDateTime.now());

        return entity;
    }

    @Override
    public List<RepairStoreItem> toRepairProjectItemList(List<RepairStoreItemEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairStoreItemEntity> toRepairProjectItemEntityList(List<RepairStoreItem> domains) {
        if (domains == null) {
            return null;
        }
        return domains.stream()
                .map(this::toRepairProjectItemEntity)
                .collect(Collectors.toList());
    }
}