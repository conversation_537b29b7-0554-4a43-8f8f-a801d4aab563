package com.inboyu.order.infrastructure.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.time.LocalTime;

/**
 * 报修配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_config")
@DynamicInsert
@DynamicUpdate
public class RepairConfigEntity extends BaseEntity {

    @Column(name = "config_id")
    private Long configId; // 配置业务ID

    @Column(name = "store_id")
    private Long storeId; // 门店ID

    @Column(name = "repair_start_time")
    private LocalTime repairStartTime; // 报修开始时间

    @Column(name = "repair_end_time")
    private LocalTime repairEndTime; // 报修结束时间

    @Column(name = "repair_time_interval")
    private Integer repairTimeInterval; // 报修时段时长

    @Column(name = "repair_week_day")
    private String repairWeekDay; // 每周服务日，字典维护

    @Column(name = "max_repair_days")
    private Integer maxRepairDays; // 最远可预约日期（从次日开始算天数）

    @Column(name = "repair_today_enable")
    private String repairTodayEnable; // 是否开放当天预约，字段维护
}
