package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.StoreId;
import com.inboyu.order.infrastructure.entity.RepairConfigEntity;
import com.inboyu.order.infrastructure.builder.RepairConfigBuilder;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修配置Builder实现
 */
@Component
public class RepairConfigBuilderImpl implements RepairConfigBuilder {

}
