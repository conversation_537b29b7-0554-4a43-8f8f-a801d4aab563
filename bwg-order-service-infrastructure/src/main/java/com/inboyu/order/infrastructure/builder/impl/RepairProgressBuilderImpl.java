package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.ProgressType;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;
import com.inboyu.order.infrastructure.builder.RepairProgressBuilder;
import com.inboyu.order.infrastructure.entity.RepairProgressEntity;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修进度Builder实现类
 */
@Component
public class RepairProgressBuilderImpl implements RepairProgressBuilder {

    @Override
    public RepairProgress toDomain(RepairProgressEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairProgress.builder()
                .id(entity.getId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion() != null ? entity.getVersion().longValue() : null)
                .deleted(entity.getDeleted())
                .progressId(entity.getProgressId())
                .repairOrderId(RepairOrderId.of(entity.getRepairOrderId()))
                .userId(entity.getUserId())
                .userName(entity.getUserName())
                .detail(entity.getDetail())
                .type(ProgressType.of(entity.getType()))
                .status(RepairOrderStatus.of(entity.getStatus()))
                .comment(entity.getComment())
                .distributeReason(entity.getDistributeReason())
                .distributeDetail(entity.getDistributeDetail())
                .isReopen(entity.getIsReopen())
                .reopenReason(entity.getReopenReason())
                .processMethod(entity.getProcessMethod())
                .assigneeId(entity.getAssigneeId())
                .assigneeName(entity.getAssigneeName())
                .build();
    }

    @Override
    public RepairProgressEntity toEntity(RepairProgress domain) {
        if (domain == null) {
            return null;
        }

        RepairProgressEntity entity = new RepairProgressEntity();
        entity.setId(domain.getId());
        entity.setCreateTime(domain.getCreateTime());
        entity.setModifyTime(domain.getModifyTime());
        entity.setVersion(domain.getVersion() != null ? domain.getVersion().intValue() : null);
        entity.setDeleted(domain.getDeleted());
        entity.setProgressId(domain.getProgressId());
        entity.setRepairOrderId(domain.getRepairOrderId() != null ? domain.getRepairOrderId().getValue() : null);
        entity.setUserId(domain.getUserId());
        entity.setUserName(domain.getUserName());
        entity.setDetail(domain.getDetail());
        entity.setType(domain.getType() != null ? domain.getType().getCode() : null);
        entity.setStatus(domain.getStatus() != null ? convertStatusToInteger(domain.getStatus()) : null);
        entity.setComment(domain.getComment());
        entity.setDistributeReason(domain.getDistributeReason());
        entity.setDistributeDetail(domain.getDistributeDetail());
        entity.setIsReopen(domain.getIsReopen());
        entity.setReopenReason(domain.getReopenReason());
        entity.setProcessMethod(domain.getProcessMethod());
        entity.setAssigneeId(domain.getAssigneeId());
        entity.setAssigneeName(domain.getAssigneeName());

        return entity;
    }

    @Override
    public List<RepairProgress> toDomains(List<RepairProgressEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairProgressEntity> toEntities(List<RepairProgress> domains) {
        if (domains == null) {
            return null;
        }
        return domains.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取进度类型标题
     */
    private String getProgressTypeTitle(Integer type) {
        if (type == null) return null;
        switch (type) {
            case 1: return "租客";
            case 2: return "管家";
            case 3: return "店长";
            case 4: return "系统";
            default: return "未知类型";
        }
    }

    /**
     * 获取状态标题
     */
    private String getStatusTitle(Integer status) {
        if (status == null) return null;
        switch (status) {
            case 1: return "已提单";
            case 2: return "处理中";
            case 3: return "已完成";
            case 4: return "已关闭";
            default: return "未知状态";
        }
    }
    
    /**
     * 将RepairOrderStatus转换为Integer类型
     */
    private Integer convertStatusToInteger(RepairOrderStatus status) {
        if (status == null) {
            return null;
        }
        
        String statusCode = status.getCode();
        if ("repair_order_status.submitted".equals(statusCode)) {
            return 1;
        } else if ("repair_order_status.processing".equals(statusCode)) {
            return 2;
        } else if ("repair_order_status.completed".equals(statusCode)) {
            return 3;
        } else if ("repair_order_status.closed".equals(statusCode)) {
            return 4;
        } else {
            throw new IllegalArgumentException("无效的报修状态代码: " + statusCode);
        }
    }
}
