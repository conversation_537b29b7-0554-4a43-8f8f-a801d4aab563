package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.RepairStoreItem;
import com.inboyu.order.domain.repair.model.ProjectId;
import com.inboyu.order.domain.repair.repository.RepairProjectItemRepository;
import com.inboyu.order.infrastructure.dao.RepairProjectItemDAO;
import com.inboyu.order.infrastructure.entity.RepairProjectItemEntity;
import com.inboyu.order.infrastructure.builder.RepairProjectItemBuilder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目物品配置仓储实现
 */
@Repository
public class RepairProjectItemRepositoryImpl implements RepairProjectItemRepository {

    @Autowired
    private RepairProjectItemDAO repairProjectItemDAO;

    @Autowired
    private RepairProjectItemBuilder repairProjectItemBuilder;

    @Override
    public Optional<RepairStoreItem> findById(Long configId) {
        Optional<RepairProjectItemEntity> entityOptional = repairProjectItemDAO.findById(configId);
        return entityOptional.map(repairProjectItemBuilder::toRepairProjectItem);
    }

    @Override
    public List<RepairStoreItem> findByProjectId(ProjectId projectId) {
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findByProjectId(projectId.getValue());
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairStoreItem> findByItemId(Long itemId) {
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findByItemId(itemId);
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<RepairStoreItem> findByProjectIdAndItemId(ProjectId projectId, Long itemId) {
        Optional<RepairProjectItemEntity> entityOptional = repairProjectItemDAO.findByProjectIdAndItemId(projectId.getValue(), itemId);
        return entityOptional.map(repairProjectItemBuilder::toRepairProjectItem);
    }

    @Override
    public List<RepairStoreItem> findBySortBetween(Integer minSort, Integer maxSort) {
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findBySortBetween(minSort, maxSort);
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairStoreItem> findByProjectIdAndSortBetween(ProjectId projectId, Integer minSort, Integer maxSort) {
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findByProjectIdAndSortBetween(projectId.getValue(), minSort, maxSort);
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairStoreItem> findByProjectIdOrderBySort(ProjectId projectId) {
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findByProjectIdOrderBySort(projectId.getValue());
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairStoreItem> findByProjectIdAndFilters(ProjectId projectId, String filterBy, String filter, Integer pageNum, Integer pageSize) {
        // TODO: 实现分页查询逻辑
        List<RepairProjectItemEntity> entities = repairProjectItemDAO.findByProjectId(projectId.getValue());
        return entities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public long countAll() {
        return repairProjectItemDAO.count();
    }

    @Override
    public long countByProjectId(ProjectId projectId) {
        return repairProjectItemDAO.countByProjectId(projectId.getValue());
    }

    @Override
    public long countByItemId(Long itemId) {
        return repairProjectItemDAO.countByItemId(itemId);
    }

    @Override
    public RepairStoreItem save(RepairStoreItem repairStoreItem) {
        RepairProjectItemEntity entity = repairProjectItemBuilder.toRepairProjectItemEntity(repairStoreItem);
        RepairProjectItemEntity savedEntity = repairProjectItemDAO.save(entity);
        return repairProjectItemBuilder.toRepairProjectItem(savedEntity);
    }

    @Override
    public List<RepairStoreItem> saveAll(List<RepairStoreItem> repairStoreItems) {
        List<RepairProjectItemEntity> entities = repairStoreItems.stream()
                .map(repairProjectItemBuilder::toRepairProjectItemEntity)
                .collect(Collectors.toList());
        List<RepairProjectItemEntity> savedEntities = repairProjectItemDAO.saveAll(entities);
        return savedEntities.stream()
                .map(repairProjectItemBuilder::toRepairProjectItem)
                .collect(Collectors.toList());
    }

    @Override
    public RepairStoreItem update(RepairStoreItem repairStoreItem) {
        RepairProjectItemEntity entity = repairProjectItemBuilder.toRepairProjectItemEntity(repairStoreItem);
        RepairProjectItemEntity updatedEntity = repairProjectItemDAO.save(entity);
        return repairProjectItemBuilder.toRepairProjectItem(updatedEntity);
    }

    @Override
    public void deleteById(Long configId) {
        repairProjectItemDAO.deleteById(configId);
    }

    @Override
    public void deleteByProjectId(ProjectId projectId) {
        repairProjectItemDAO.deleteByProjectId(projectId.getValue(), true);
    }

    @Override
    public void deleteByItemId(Long itemId) {
        repairProjectItemDAO.deleteByItemId(itemId, true);
    }

    @Override
    public void deleteByProjectIdAndItemId(ProjectId projectId, Long itemId) {
        repairProjectItemDAO.deleteByProjectIdAndItemId(projectId.getValue(), itemId, true);
    }

    @Override
    public void updateSortById(Long configId, Integer sort) {
        repairProjectItemDAO.updateSortById(configId, sort);
    }

    @Override
    public void updateSortByIds(List<Long> configIds, List<Integer> sorts) {
        // TODO: 实现批量更新排序权重
        for (int i = 0; i < configIds.size() && i < sorts.size(); i++) {
            updateSortById(configIds.get(i), sorts.get(i));
        }
    }

    @Override
    public void reorderByProjectId(ProjectId projectId, List<Long> itemIds) {
        // TODO: 实现重新排序项目物品配置
        for (int i = 0; i < itemIds.size(); i++) {
            Optional<RepairStoreItem> itemOptional = findByProjectIdAndItemId(projectId, itemIds.get(i));
            if (itemOptional.isPresent()) {
                RepairStoreItem item = itemOptional.get();
                item.updateSort(i + 1);
                update(item);
            }
        }
    }
}
