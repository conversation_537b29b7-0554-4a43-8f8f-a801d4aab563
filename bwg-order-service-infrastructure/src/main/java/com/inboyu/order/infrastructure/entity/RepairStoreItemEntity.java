package com.inboyu.order.infrastructure.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 项目物品配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_project_item")
@DynamicInsert
@DynamicUpdate
public class RepairProjectItemEntity extends BaseEntity {

    @Column(name = "config_id")
    private Long configId; // 配置业务ID

    @Column(name = "project_id")
    private Long projectId; // 项目ID

    @Column(name = "item_id")
    private Long itemId; // 物品ID

    private Integer sort; // 排序权重
}
