package com.inboyu.order.app.controller;

import com.inboyu.order.app.application.repair.RepairAppService;
import com.inboyu.order.app.dto.request.repair.RepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderCreateResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderDetailResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 报修工单控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/repair/orders")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Tag(name = "报修工单管理", description = "报修工单相关接口")
public class RepairController {

    private final RepairAppService repairAppService;

    /**
     * 创建报修工单
     */
    @PostMapping
    @Operation(summary = "创建报修工单", description = "创建新的报修工单")
    public ResponseEntity<RepairOrderCreateResponseDTO> createRepairOrder(
            @Valid @RequestBody RepairOrderCreateRequestDTO request) {
        log.info("创建报修工单请求: {}", request);
        
        try {
            RepairOrderCreateResponseDTO response = repairAppService.createRepairOrder(request);
            log.info("创建报修工单成功: {}", response);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建报修工单失败", e);
            throw e;
        }
    }

    /**
     * 获取报修工单详情
     */
    @GetMapping("/{repairOrderId}")
    @Operation(summary = "获取报修工单详情", description = "根据报修工单ID获取详细信息")
    public ResponseEntity<RepairOrderDetailResponseDTO> getRepairOrderDetail(
            @PathVariable Long repairOrderId) {
        log.info("获取报修工单详情请求: repairOrderId={}", repairOrderId);
        
        try {
            RepairOrderDetailResponseDTO response = repairAppService.getRepairOrderDetail(repairOrderId);
            log.info("获取报修工单详情成功: {}", response);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取报修工单详情失败: repairOrderId={}", repairOrderId, e);
            throw e;
        }
    }
}
