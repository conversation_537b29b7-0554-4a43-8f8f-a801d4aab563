package com.inboyu.order.app.dto.response.repair;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报修工单详情响应DTO
 */
@Data
public class RepairOrderDetailResponseDTO {

    /**
     * 报修工单ID
     */
    private Long repairOrderId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 楼栋ID
     */
    private Long buildingId;

    /**
     * 报修物品ID
     */
    private Long itemId;

    /**
     * 工作流ID
     */
    private Long workflowId;

    /**
     * 报修区域
     */
    private String area;

    /**
     * 问题描述
     */
    private String detail;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 报修人姓名
     */
    private String submitter;

    /**
     * 意向维修日期
     */
    private LocalDate willRepairDate;

    /**
     * 意向维修开始时间段
     */
    private Integer willRepairStart;

    /**
     * 意向维修结束时间段
     */
    private Integer willRepairEnd;

    /**
     * 是否无人可直接入户
     */
    private Boolean isEnter;

    /**
     * 报修状态：1-已提单 2-处理中 3-已完成 4-已关闭
     */
    private Integer status;

    /**
     * 工单类型：1-在住报修 2-空房报修 3-退房报修
     */
    private Integer orderType;

    /**
     * 标签状态：0-正常 1-超时 2-挂单 3-返工
     */
    private Integer tagStatus;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 挂起原因
     */
    private String suspendReason;

    /**
     * 预计上门时间
     */
    private LocalDateTime preDoorTime;

    /**
     * 预计完成时间
     */
    private LocalDateTime preRepairTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 受理时间
     */
    private LocalDateTime dealTime;

    /**
     * 责任方ID
     */
    private Long responsibleId;

    /**
     * 处理人ID
     */
    private Long maintainerId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 进度记录列表
     */
    private List<RepairProgressResponseDTO> progressList;

    /**
     * 附件列表
     */
    private List<RepairAttachmentResponseDTO> attachmentList;
}
