package com.inboyu.operation.domain.dict.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public final class Dict {
    private DictCode code;
    private DictCode type;		// 分类编码
    private String title;				// 名称
    private String icon;				// 图标
    private String remark;				// 说明
    private Integer index;				// 排序
    private Integer hidden;				// 是否隐藏，隐藏运营商不可见
}
