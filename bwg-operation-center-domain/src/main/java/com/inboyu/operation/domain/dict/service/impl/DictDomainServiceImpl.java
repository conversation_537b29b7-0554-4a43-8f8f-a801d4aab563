package com.inboyu.operation.domain.dict.service.impl;

import com.inboyu.operation.domain.dict.model.Dict;
import com.inboyu.operation.domain.dict.repository.DictRepository;
import com.inboyu.operation.domain.dict.service.DictDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DictDomainServiceImpl implements DictDomainService {

    @Autowired
    DictRepository dictRepository;

    @Override
    public List<Dict> getDictList(String type) {
        return dictRepository.getDictList(type);
    }
}
