package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.repository.CustomerListRepository;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.infrastructure.builder.CustomerListItemBuilder;
import com.inboyu.sales.infrastructure.dao.PlatformCustomerDao;
import com.inboyu.sales.infrastructure.dao.ReserveSeeHouseDao;
import com.inboyu.sales.infrastructure.dao.SaleCycleDao;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户列表仓储实现
 * 只负责数据访问，不包含业务逻辑
 */
@Repository
@Slf4j
public class CustomerListRepositoryImpl implements CustomerListRepository {

    @Autowired
    private SaleCycleDao saleCycleDao;

    @Autowired
    private PlatformCustomerDao platformCustomerDao;

    @Autowired
    private ReserveSeeHouseDao reserveSeeHouseDao;

    @Autowired
    private CustomerListItemBuilder customerListItemBuilder;

    @Override
    public Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, StoreId storeId) {
        log.info("仓储层查询客户列表，页码：{}，每页条数：{}，门店ID：{}", pageNum, pageSize, storeId);

        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);

        // 1. 查询销售周期分页数据（按创建时间降序排序，客户去重）
        Page<SaleCycleEntity> saleCyclePage = saleCycleDao.findCustomerListByStore(storeId.getValue(), pageable);

        // 2. 转换为客户列表项
        List<CustomerListItem> customerListItems = saleCyclePage.getContent().stream()
                .map(this::convertToCustomerListItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 3. 构造分页结果
        return new Pagination<>(
                saleCyclePage.getNumber() + 1,
                saleCyclePage.getSize(),
                saleCyclePage.getTotalPages(),
                saleCyclePage.getTotalElements(),
                customerListItems);
    }

    /**
     * 转换为客户列表项
     */
    private CustomerListItem convertToCustomerListItem(SaleCycleEntity saleCycle) {
        // 查询平台客户信息
        PlatformCustomerEntity platformCustomer = platformCustomerDao
                .findByPlatformCustomerIdAndDeleted(saleCycle.getPlatformCustomerId(), DeleteFlag.NOT_DELETED);
        
        if (platformCustomer == null) {
            log.warn("未找到平台客户信息，platformCustomerId：{}", saleCycle.getPlatformCustomerId());
            return null;
        }
        
        // 查询预约记录
        List<ReserveSeeHouseEntity> reservations = reserveSeeHouseDao
                .findCustomerReservationInSaleCycle(saleCycle.getCustomerId(), saleCycle.getStoreId(), saleCycle.getStartTime());

        // 取第一条记录（DAO层已按业务规则排序）
        ReserveSeeHouseEntity firstReservation = reservations.isEmpty() ? null : reservations.getFirst();

        return customerListItemBuilder.buildCustomerListItem(saleCycle, platformCustomer, firstReservation);
    }
}
