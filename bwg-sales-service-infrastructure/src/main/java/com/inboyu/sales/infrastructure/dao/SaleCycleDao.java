package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * 销售周期DAO
 */
public interface SaleCycleDao extends JpaRepository<SaleCycleEntity, Long> {

    /**
     * 根据销售周期ID查询
     *
     * @param saleCycleId 销售周期ID
     * @param deleted     删除标志
     * @return 销售周期实体
     */
    SaleCycleEntity findBySaleCycleIdAndDeleted(Long saleCycleId, Integer deleted);


    /**
     * 获取平台级销售周期
     * @param platformCustomerId
     * @return
     */
    @Query("SELECT s FROM SaleCycleEntity s WHERE s.deleted = 0 " +
            "AND s.platformCustomerId = :platformCustomerId " +
            "AND s.storeId = 0")
    SaleCycleEntity findPlatformSaleCycle(@Param("platformCustomerId") Long platformCustomerId);

    /**
     * 获取门店级销售周期
     * @param platformCustomerId
     * @param storeId
     * @param platformSaleCycleId
     * @return
     */
    @Query("SELECT s FROM SaleCycleEntity s WHERE s.deleted = 0 " +
            "AND s.platformCustomerId = :platformCustomerId " +
            "AND s.storeId = :storeId " +
            "AND s.platformSaleCycleId = :platformSaleCycleId")
    SaleCycleEntity findStoreSaleCycle(@Param("platformCustomerId") Long platformCustomerId,
                                       @Param("storeId") Long storeId,
                                       @Param("platformSaleCycleId") Long platformSaleCycleId);

    /**
     * 分页查询门店客户列表（按销售周期创建时间降序，客户去重）
     *
     * @param storeId  门店ID（可选）
     * @param pageable 分页参数
     * @return 分页的销售周期列表
     */
    @Query(value = """
            SELECT sc FROM SaleCycleEntity sc
            WHERE sc.deleted = 0
            AND sc.storeId = :storeId
            AND sc.platformSaleCycleId != 0
            AND sc.startTime = (
                SELECT MAX(sc2.startTime)
                FROM SaleCycleEntity sc2
                WHERE sc2.deleted = 0
                AND sc2.customerId = sc.customerId
                AND sc2.storeId = :storeId
            )
            ORDER BY sc.startTime DESC
            """,
            countQuery = """
                    SELECT COUNT(DISTINCT sc.customerId)
                    FROM SaleCycleEntity sc
                    WHERE sc.deleted = 0
                    AND sc.storeId = :storeId
                    """)
    Page<SaleCycleEntity> findCustomerListByStore(@Param("storeId") Long storeId, Pageable pageable);
}
