package com.inboyu.sales.infrastructure.repository;

import com.inboyu.admin.api.StoreFeignClient;
import com.inboyu.admin.dto.response.StoreDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.sales.domain.store.model.Store;
import com.inboyu.sales.domain.store.repository.StoreRepository;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 门店仓储实现
 */
@Repository
@Slf4j
public class StoreRepositoryImpl implements StoreRepository {

    @Autowired
    private FeignAccessor feignAccessor;

    @Override
    public Store getStore(String storeId) {
        log.info("通过Feign客户端获取门店信息，门店ID：{}", storeId);

        try {

            StoreFeignClient storeFeignClient = feignAccessor.get(StoreFeignClient.class);
            StoreDetailResponseDTO storeDetail = storeFeignClient.getStore(storeId.toString());

            if (storeDetail == null) {
                log.warn("门店信息不存在，门店ID：{}", storeId);
                return null;
            }

            // 将DTO转换为领域模型
            return Store.builder()
                    .storeId(Long.valueOf(storeId))
                    .storeName(storeDetail.getTitle())
                    .storeAddress(storeDetail.getAddress())
                    .build();

        } catch (Exception e) {
            log.error("获取门店信息失败，门店ID：{}，错误信息：{}", storeId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Store> filterByStoreIds(String[] storeIds) {
        log.info("通过Feign客户端批量获取门店信息，门店IDs：{}", (Object) storeIds);

        try {
            StoreFeignClient storeFeignClient = feignAccessor.get(StoreFeignClient.class);

            // 调用批量获取接口
            List<StoreDTO> storeList = storeFeignClient.filterByStoreIds(storeIds);

            if (storeList == null || storeList.isEmpty()) {
                log.warn("批量获取门店信息为空，门店IDs：{}", (Object) storeIds);
                return Collections.emptyList();
            }

            // 将DTO列表转换为领域模型列表
            List<Store> stores = new ArrayList<>();
            for (StoreDTO storeDTO : storeList) {
                stores.add(Store.builder()
                        .storeId(Long.valueOf(storeDTO.getStoreId()))
                        .storeName(storeDTO.getTitle())
                        .storeAddress(storeDTO.getAddress())
                        .build());
            }

            return stores;
        } catch (Exception e) {
            log.error("批量获取门店信息失败，错误信息：{}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
