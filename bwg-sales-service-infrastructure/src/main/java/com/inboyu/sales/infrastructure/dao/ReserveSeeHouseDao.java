package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

import java.time.LocalDate;

public interface ReserveSeeHouseDao extends JpaRepository<ReserveSeeHouseEntity, Long> {

    /**
     * 分页查询预约看房
     *
     * @param storeId    门店ID
     * @param customerId 客户ID
     * @param pageable   分页参数
     * @return {@link Page }<{@link ReserveSeeHouseEntity }>
     */
    @Query("SELECT r FROM ReserveSeeHouseEntity r WHERE r.deleted = 0 AND (:storeId IS NULL OR r.storeId = :storeId) AND (:customerId IS NULL OR r.customerId = :customerId) ORDER BY r.createTime DESC")
    Page<ReserveSeeHouseEntity> pageByCustomerIdAndStoreId(Long customerId, Long storeId, Pageable pageable);


    /**
     * @param reserveSeeHouseId
     * @param deleted
     * @return
     */
    ReserveSeeHouseEntity findByReserveSeeHouseIdAndDeleted(Long reserveSeeHouseId, int deleted);

    /**
     * 根据客户ID、门店ID和预约日期查询最新的预约成功记录
     *
     * @param customerId  客户ID
     * @param storeId     门店ID
     * @param reserveDate 预约日期
     * @param roomId
     * @param roomTypeId
     * @param reserveDate
     * @param bookedStatus
     * @return {@link ReserveSeeHouseEntity} 最新的预约成功记录，如果没有则返回null
     */
    @Query("SELECT r FROM ReserveSeeHouseEntity r WHERE r.deleted = 0 " +
            "AND r.customerId = :customerId " +
            "AND r.storeId = :storeId " +
            "AND r.reserveDate = :reserveDate " +
            "AND r.seeState = :bookedStatus " +
            "AND r.roomId = :roomId " +
            "AND r.roomTypeId = :roomTypeId " +
            "ORDER BY r.createTime DESC LIMIT 1")
    ReserveSeeHouseEntity findLatestBookedReservation(
            @Param("customerId") Long customerId,
            @Param("storeId") Long storeId,
            @Param("roomId") Long roomId,
            @Param("roomTypeId") Long roomTypeId,
            @Param("reserveDate") LocalDate reserveDate,
            @Param("bookedStatus") String bookedStatus
    );

    /**
     * 查询客户在指定门店和销售周期中的预约看房记录
     * 优先返回成功预约状态的记录（按结束时间最近排序），如无则返回最近一条记录
     *
     * @param customerId 客户ID
     * @param storeId 门店ID
     * @param saleCycleStartTime 销售周期开始时间（用于筛选当前销售周期的预约）
     * @return 预约看房记录
     */
    @Query("""
        SELECT r FROM ReserveSeeHouseEntity r
        WHERE r.deleted = 0
        AND r.customerId = :customerId
        AND r.storeId = :storeId
        ORDER BY
            CASE WHEN r.seeState = 'reserve_see_house_see_state.booked' THEN 0 ELSE 1 END,
            r.endTime DESC,
            r.createTime DESC
        """)
    List<ReserveSeeHouseEntity> findCustomerReservationInSaleCycle(
        @Param("customerId") Long customerId,
        @Param("storeId") Long storeId,
        @Param("saleCycleStartTime") LocalDateTime saleCycleStartTime);
}