package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 合同模板实体
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_contract_template")
public class ContractTemplateEntity extends BaseEntity {

    @Schema(title = "门店Id")
    @Column(name = "store_id")
    private Long storeId;

    @Schema(title = "在线合同条款")
    @Column(name = "online_contract")
    private String onlineContract;

    @Schema(title = "电子签章编号")
    @Column(name = "esign_number")
    private String esignNumber;

    @Schema(title = "缴费周期")
    @Column(name = "payment_cycle")
    private String paymentCycle;

    @Schema(title = "推送方式")
    @Column(name = "push_type")
    private String pushType;

    @Schema(title = "起租日期可选范围")
    @Column(name = "rent_date_range")
    private Integer rentDateRange;

    @Schema(title = "滞纳金比例")
    @Column(name = "overdue_fine_proportion")
    private BigDecimal overdueFineProportion;
}
