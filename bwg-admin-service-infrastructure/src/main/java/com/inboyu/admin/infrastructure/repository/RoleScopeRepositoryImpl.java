package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.role.repository.RoleScopeRepository;
import com.inboyu.admin.infrastructure.builder.RoleScopeBuilder;
import com.inboyu.admin.infrastructure.dao.RoleScopeDao;
import com.inboyu.admin.infrastructure.dao.entity.RoleScopeEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月31日 16:11
 */
@Repository
public class RoleScopeRepositoryImpl implements RoleScopeRepository {
    @Autowired
    private RoleScopeDao roleScopeDao;

    @Autowired
    private RoleScopeBuilder roleScopeBuilder;

    /**
     * 批量保存角色权限
     *
     * @param rolePermissions
     * <AUTHOR>
     * @date 2025/07/31 16:14:34
     */
    @Override
    public void saveAll(List<RolePermission> rolePermissions) {
        List<RoleScopeEntity> roleScopeEntities = roleScopeBuilder.toEntities(rolePermissions);
        roleScopeDao.saveAll(roleScopeEntities);
    }

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR> Dong
     * @date 2025/07/31 17:09:04
     */
    @Override
    public List<RolePermission> findByRoleId(RoleId roleId) {
        List<RoleScopeEntity> roleScopeEntities = roleScopeDao.findByRoleIdAndDeleted(roleId.getValue(), DeleteFlag.NOT_DELETED);
        return roleScopeBuilder.toRolePermission(roleScopeEntities);
    }

    /**
     * 根据角色id删除角色权限
     *
     * @param id
     * <AUTHOR> Dong
     * @date 2025/07/31 19:45:55
     */
    @Override
    public void deleteByRoleId(RoleId id) {
        roleScopeDao.deleteByRoleId(id.getValue());
    }

    /**
     * 通过角色id查询角色权限
     *
     * @param roleIds
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR> Dong
     * @date 2025/08/01 09:51:46
     */
    @Override
    public List<RolePermission> findByRoleIds(List<RoleId> roleIds) {
        List<RoleScopeEntity> roleScopes = roleScopeDao.findByRoleIdInAndDeleted(roleIds.stream().map(RoleId::getValue).toList(), DeleteFlag.NOT_DELETED);
        return roleScopeBuilder.toRolePermission(roleScopes);
    }
}
