package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.customer.model.Customer;
import com.inboyu.admin.domain.customer.model.CustomerReal;
import com.inboyu.admin.domain.customer.repository.CustomerRepository;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.admin.infrastructure.builder.CustomerBuilder;
import com.inboyu.admin.infrastructure.dao.CustomerDao;
import com.inboyu.admin.infrastructure.dao.CustomerRealDao;
import com.inboyu.admin.infrastructure.dao.entity.CustomerEntity;
import com.inboyu.admin.infrastructure.dao.entity.CustomerRealEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.jpa.converter.SensitiveConverter;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class CustomerRepositoryImpl implements CustomerRepository {

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private CustomerBuilder customerBuilder;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private CustomerRealDao customerRealDao;

    @Override
    public Customer save(Customer customer) {
        CustomerEntity customerEntity = customerBuilder.toCustomerEntify(customer);
        customerEntity.setCustomerId(snowflakeIdGenerator.nextId());
        CustomerEntity saveEntify = customerDao.save(customerEntity);
        return customerBuilder.toCustomer(saveEntify);
    }

    @Override
    public List<Customer> list(String keywords) {
        keywords = "%" + keywords + "%";
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<CustomerEntity> query = cb.createQuery(CustomerEntity.class);
        Root<CustomerEntity> root = query.from(CustomerEntity.class);
        query.select(root)
                .where(
                        cb.and(
                                cb.or(
                                        cb.like(root.get("name"), keywords),
                                        cb.like(root.get("mobile"), SensitiveConverter.encryptLike(keywords))
                                ),
                                cb.equal(root.get("deleted"), DeleteFlag.NOT_DELETED)
                        )
                )
                .orderBy(cb.desc(root.get("createTime")));
        try {
            List<CustomerEntity> customerEntifies = entityManager.createQuery(query).getResultList();
            return customerBuilder.toCustomertList(customerEntifies);
        } catch (NoResultException e) {
            log.error(e.getLocalizedMessage());
            throw new AppException(ResponseCode.CUSTOMER_STATUS_ERROR);
        }
    }

    @Override
    public Customer findByPhoneNumber(String phoneNumber) {
        CustomerEntity customerEntity = customerDao.findByMobileLikeAndDeleted(phoneNumber, DeleteFlag.NOT_DELETED);
        if (Objects.isNull(customerEntity)) {
            return null;
        }
        return customerBuilder.toCustomer(customerEntity);
    }

    @Override
    public Customer getByCustomerId(String customerId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<CustomerEntity> query = cb.createQuery(CustomerEntity.class);
        Root<CustomerEntity> root = query.from(CustomerEntity.class);
        query.select(root)
        .where(
                cb.and(
                        cb.equal(root.get("customerId"), customerId),
                        cb.equal(root.get("deleted"), DeleteFlag.NOT_DELETED)
                )
        );
        try {
            CustomerEntity customerEntity = entityManager.createQuery(query).getSingleResult();
            return customerBuilder.toCustomer(customerEntity);
        }   catch (NoResultException e) {
            log.error(e.getLocalizedMessage());
            throw new AppException(ResponseCode.CUSTOMER_STATUS_ERROR);
        }
    }

    @Override
    public CustomerReal saveReal(CustomerReal customerReal) {
        CustomerRealEntity customerRealEntity = customerBuilder.toCustomerRealEntify(customerReal);
        customerRealEntity.setId(snowflakeIdGenerator.nextId());
        CustomerRealEntity saveEntify = customerRealDao.save(customerRealEntity);
        return customerBuilder.toCustomerReal(saveEntify,customerReal);
    }
}
