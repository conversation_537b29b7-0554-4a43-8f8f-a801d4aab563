package com.inboyu.admin.infrastructure.dao;


import com.inboyu.admin.infrastructure.dao.entity.CustomerEntity;
import com.inboyu.spring.cloud.starter.jpa.annotation.EncryptedLike;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CustomerDao extends JpaRepository<CustomerEntity, Long> {

    CustomerEntity findByMobileLikeAndDeleted(@EncryptedLike String mobile, Integer deleted);
}
