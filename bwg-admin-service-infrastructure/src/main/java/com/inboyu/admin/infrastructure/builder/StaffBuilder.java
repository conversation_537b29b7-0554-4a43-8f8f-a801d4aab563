package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.UserId;
import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.admin.event.StoreCreateEvent;
import com.inboyu.admin.infrastructure.dao.entity.StaffEntity;

/**
 * <AUTHOR>
 * @date 2025年08月05日 14:02
 */
public interface StaffBuilder {
    /**
     * 将领域对象转换为数据库实体
     *
     * @param staff 员工领域对象
     * @return 员工数据库实体
     */
    StaffEntity toStaffEntity(Staff staff);

    /**
     * 将数据库实体转换为领域对象
     *
     * @param entity 员工数据库实体
     * @return 员工领域对象
     */
    Staff toStaff(StaffEntity entity);

    /**
     * 将领域对象转换为消息实体
     *
     * @param userId
     * @param staff
     * @return {@link StoreCreateEvent }
     * <AUTHOR> Dong
     * @date 2025/08/05 16:53:45
     */
    StaffCreateEvent toCreateEvent(UserId userId, Staff staff);

    StaffEntity toUpdateEntity(Staff staff);
}
