package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.infrastructure.dao.entity.StoreEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月01日 11:36
 */
public interface StoreDao extends JpaRepository<StoreEntity, Long> {
    /**
     * 根据门店名称统计门店数量
     *
     * @param title
     * @param deleted
     * @return int
     * <AUTHOR>
     * @date 2025/08/02 14:51:54
     */
    int countByTitleAndDeleted(String title, int deleted);

    /**
     * 根据门店名称和门店ID统计门店数量（用于更新时排除自己）
     *
     * @param title
     * @param storeId
     * @param deleted
     * @return int
     * <AUTHOR>
     * @date 2025/08/02 16:00
     */
    int countByTitleAndStoreIdNotAndDeleted(String title, Long storeId, int deleted);

    /**
     * 根据门店ID查询门店
     *
     * @param storeId
     * @param deleted
     * @return StoreEntity
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    StoreEntity findByStoreIdAndDeleted(Long storeId, int deleted);

    /**
     * 更新门店信息
     *
     * @param storeId
     * @param storeEntity
     * <AUTHOR> Dong
     * @date 2025/08/02 16:50
     */
    @Modifying
    @Query("UPDATE StoreEntity s SET s.deptId = :#{#storeEntity.deptId}, s.title = :#{#storeEntity.title}, " +
            "s.provinceCode = :#{#storeEntity.provinceCode}, s.provinceName = :#{#storeEntity.provinceName}, " +
            "s.cityCode = :#{#storeEntity.cityCode}, s.cityName = :#{#storeEntity.cityName}, " +
            "s.countyCode = :#{#storeEntity.countyCode}, s.countyName = :#{#storeEntity.countyName}, " +
            "s.address = :#{#storeEntity.address}, s.coordinate = :#{#storeEntity.coordinate}, s.version = s.version + 1 " +
            "WHERE s.storeId = :storeId AND s.deleted = 0")
    void updateStore(Long storeId, StoreEntity storeEntity);

    /**
     * 分页查询门店（支持多组织ID）
     *
     * @param deptIds 组织ID列表
     * @param title   门店名称关键词
     * @param pageable 分页参数
     * @return {@link Page }<{@link StoreEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:40:33
     */
    @Query("SELECT s FROM StoreEntity s WHERE s.deleted = 0 AND (:deptIds IS NULL OR s.deptId IN :deptIds) AND (:title IS NULL OR s.title LIKE %:title%) ORDER BY s.createTime DESC")
    Page<StoreEntity> pageByTitleLikeAndDeptIds(List<Long> deptIds, String title, Pageable pageable);

    /**
     * 批量查询组织下门店数
     *
     * @param deptId
     * @param deleted
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 19:45:15
     */
    Integer countByDeptIdInAndDeleted(List<Long> deptId, Integer deleted);

    /**
     * 根据门店ID列表批量查询门店
     *
     * @param storeIds 门店ID列表
     * @param deleted 删除标识
     * @return {@link List }<{@link StoreEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<StoreEntity> findByStoreIdInAndDeleted(List<Long> storeIds, Integer deleted);

    /**
     *
     * @param deptIds
     * @param deleted
     * @return
     */
    List<StoreEntity> findByDeptIdInAndDeleted(List<Long> deptIds, int deleted);
}
