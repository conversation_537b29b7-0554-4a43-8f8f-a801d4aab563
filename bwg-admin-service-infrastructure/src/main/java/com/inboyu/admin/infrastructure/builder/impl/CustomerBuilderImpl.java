package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.customer.model.*;
import com.inboyu.admin.infrastructure.builder.CustomerBuilder;
import com.inboyu.admin.infrastructure.dao.entity.CustomerEntity;
import com.inboyu.admin.infrastructure.dao.entity.CustomerRealEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerBuilderImpl implements CustomerBuilder {

    @Override
    public Customer toCustomer(CustomerEntity entity) {
        Customer customer = Customer.of(
                CustomerId.of(entity.getCustomerId()),
                entity.getName(),
                entity.getMobile()
        );
        return customer;
    }

    @Override
    public CustomerEntity toCustomerEntify(Customer customer) {
        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.setName(customer.getName());
        customerEntity.setMobile(customer.getMobile());
        customerEntity.setDeleted(DeleteFlag.NOT_DELETED);
        return customerEntity;
    }

    @Override
    public List<Customer> toCustomertList(List<CustomerEntity> entityList) {
        return entityList.stream().map(this::toCustomer).collect(Collectors.toList());
    }

    @Override
    public CustomerRealEntity toCustomerRealEntify(CustomerReal customerReal) {
        CustomerRealEntity customerRealEntity = new CustomerRealEntity();
        customerRealEntity.setCertificateType(customerReal.getCertificateType().getCode());
        customerRealEntity.setCertificateNumber(customerReal.getCertificateNumber().getValue());
        customerRealEntity.setIdCardFront(customerReal.getIdCardFront());
        customerRealEntity.setIdCardBack(customerReal.getIdCardBack());
        customerRealEntity.setMailAddress(customerReal.getMailAddress());
        customerRealEntity.setMail(customerReal.getMail());
        customerRealEntity.setEmergencyContact(customerReal.getEmergencyContact());
        customerRealEntity.setEmergencyContactMobile(customerReal.getEmergencyContactMobile());
        return customerRealEntity;
    }

    @Override
    public CustomerReal toCustomerReal(CustomerRealEntity customerRealEntity,CustomerReal customerReal) {
        return CustomerReal.of(
                CustomerId.of(customerRealEntity.getCustomerId()),
                CertificateType.of(customerRealEntity.getCertificateType(),customerReal.getCertificateType().getCode()),
                customerRealEntity.getCertificateNumber(),
                customerRealEntity.getIdCardFront(),
                customerRealEntity.getIdCardBack(),
                customerRealEntity.getMailAddress(),
                customerRealEntity.getMail(),
                customerRealEntity.getEmergencyContact(),
                customerRealEntity.getEmergencyContactMobile()
        );

    }
}
