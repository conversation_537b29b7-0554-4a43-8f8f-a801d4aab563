package com.inboyu.admin.infrastructure.constant;

import lombok.Getter;

/**
 * 合同模板对比枚举
 * <AUTHOR>
 */
@Getter
public enum ContractTemplateDiffEnum {
    FORMAT("%s从[%s]修改为[%s]；"),
    ONLINE_CONTRACT("在线合同条款"),
    ESIGN_NUMBER("电子签章编号"),
    PAYMENT_CYCLE("缴费周期"),
    PUSH_TYPE("推送方式"),
    RENT_DATE_RANGE("起租日期可选范围"),
    OVERDUE_FINE_PROPORTION("滞纳金比例");

    private final String description;

    ContractTemplateDiffEnum(String description) {
        this.description = description;
    }
}