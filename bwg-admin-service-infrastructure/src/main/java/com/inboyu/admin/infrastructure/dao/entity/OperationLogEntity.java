package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志实体
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_operation_log")
public class OperationLogEntity extends BaseEntity {

    @Schema(title = "操作日志ID")
    @Column(name = "operation_id")
    private Long operationId;

    @Schema(title = "门店ID")
    @Column(name = "store_id")
    private Long storeId;

    @Schema(title = "员工ID")
    @Column(name = "staff_id")
    private Long staffId;

    @Schema(title = "操作人")
    @Column(name = "operator")
    private String operator;

    @Schema(title = "操作时间")
    @Column(name = "operation_time")
    private LocalDateTime operationTime;

    @Schema(title = "操作类型")
    @Column(name = "operation_type")
    private String operationType;

    @Schema(title = "标题")
    @Column(name = "title")
    private String title;

    @Schema(title = "内容")
    @Column(name = "content")
    private String content;

    @Schema(title = "附件名称")
    @Column(name = "attachment_name")
    private String attachmentName;

    @Schema(title = "附件地址")
    @Column(name = "attachment_url")
    private String attachmentUrl;

    @Schema(title = "是否成功")
    @Column(name = "success")
    private Boolean success;

    @Schema(title = "失败原因")
    @Column(name = "fail_reason")
    private String failReason;

    @Schema(title = "业务类型")
    @Column(name = "biz_type")
    private String bizType;
}