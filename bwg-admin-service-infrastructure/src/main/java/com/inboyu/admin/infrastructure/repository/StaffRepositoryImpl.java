package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.staff.model.PhoneNumber;
import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.UserId;
import com.inboyu.admin.domain.staff.repository.StaffRepository;
import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.admin.infrastructure.builder.StaffBuilder;
import com.inboyu.admin.infrastructure.dao.StaffDao;
import com.inboyu.admin.infrastructure.dao.entity.StaffEntity;
import com.inboyu.alimq.producer.RocketMQTemplate;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import com.inboyu.spring.cloud.starter.jpa.converter.SensitiveConverter;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import com.inboyu.user.api.AccountFeignClient;
import com.inboyu.user.dto.CreatePhoneAccountRequest;
import com.inboyu.user.dto.UserResponse;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 13:56
 */
@Repository
public class StaffRepositoryImpl implements StaffRepository {
    @Autowired
    private StaffDao staffDao;

    @Autowired
    private StaffBuilder staffBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private FeignAccessor feignAccessor;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private EntityManager entityManager;

    /**
     * 生成员工ID
     *
     * @return StaffId
     * <AUTHOR> Dong
     * @date 2025/08/05 11:30:47
     */
    @Override
    public StaffId generateId() {
        return StaffId.of(snowflakeIdGenerator.nextId());
    }

    /**
     * 保存员工
     *
     * @param staff
     * @return {@link Staff }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:30:53
     */
    @Override
    public Staff saveStaff(Staff staff) {
        StaffEntity entity = staffBuilder.toStaffEntity(staff);
        StaffEntity save = staffDao.save(entity);
        return staffBuilder.toStaff(save);
    }

    /**
     * 根据员工id查询员工
     *
     * @param staffId
     * @return {@link Staff }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:31:49
     */
    @Override
    public Staff findByStaffId(StaffId staffId) {
        StaffEntity entity = staffDao.findByStaffIdAndDeleted(staffId.getValue(), DeleteFlag.NOT_DELETED);
        return staffBuilder.toStaff(entity);
    }

    /**
     * 根据手机号查询员工数量
     *
     * @param phone
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/05 11:32:14
     */
    @Override
    public int countByPhone(String phone) {
        return staffDao.countByPhoneAndDeleted(phone, DeleteFlag.NOT_DELETED);
    }

    /**
     * 根据手机号查询userId
     *
     * @param phone
     * @return {@link UserId }
     * <AUTHOR> Dong
     * @date 2025/08/05 15:33:15
     */
    @Override
    public UserId findUserIdByPhone(String phone) {
        AccountFeignClient client = feignAccessor.get(AccountFeignClient.class);
        CreatePhoneAccountRequest request = new CreatePhoneAccountRequest();
        request.setPhone(phone);
        UserResponse user = client.createUserByPhone(request);
        if (null == user || null == user.getUserId()) {
            return null;
        }
        return UserId.of(user.getUserId());
    }

    /**
     * 发送员工创建事件
     *
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/05 16:54:23
     */
    @Override
    public void sendCreateEvent(Staff staff) {
        StaffCreateEvent event = staffBuilder.toCreateEvent(null, staff);
        rocketMQTemplate.send(event);
    }

    @Override
    public Pagination<Staff> pageByKeywordLike(Integer pageNum, Integer pageSize,
                                               String keyword, String status,
                                               List<Long> staffIds) {

        // 创建分页参数（页码从0开始）
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);


        // 创建Criteria查询
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<StaffEntity> query = cb.createQuery(StaffEntity.class);
        Root<StaffEntity> root = query.from(StaffEntity.class);

        // 初始条件：只查询未删除的记录
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.equal(root.get("deleted"), DeleteFlag.NOT_DELETED));

        // 添加关键字模糊查询条件（大小写不敏感）
        if (StringUtils.isNotBlank(keyword)) {
            String likePattern = SensitiveConverter.encryptLike("%" + keyword + "%");
            Predicate nameLike = cb.like(root.get("name"), likePattern);
            Predicate phoneLike = cb.like(root.get("phone"), likePattern);
            predicates.add(cb.or(nameLike, phoneLike));
        }

        // 添加状态条件
        if (StringUtils.isNotBlank(status)) {
            predicates.add(cb.equal(root.get("status"), status));
        }

        // 添加员工id集合条件
        if (CollectionUtils.isNotEmpty(staffIds)) {
            predicates.add(root.get("staffId").in(staffIds));
        }

        // 按 createTime 倒序排序（DESC）
        Path<Object> createTimePath = root.get("createTime");
        query.orderBy(cb.desc(createTimePath));

        // 组合所有条件
        query.where(predicates.toArray(new Predicate[0]));

        // 使用相同的root对象执行分页查询
        List<StaffEntity> staffEntities = entityManager.createQuery(query)
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize())
                .getResultList();

        // 使用单独的查询获取总数（避免共享root）
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        // 创建新的root
        Root<StaffEntity> countRoot = countQuery.from(StaffEntity.class);

        // 重新构建计数查询的条件
        List<Predicate> countPredicates = new ArrayList<>();
        countPredicates.add(cb.equal(countRoot.get("deleted"), DeleteFlag.NOT_DELETED));

        if (StringUtils.isNotBlank(keyword)) {
            String likePattern = SensitiveConverter.encryptLike("%" + keyword + "%");
            Predicate nameLike = cb.like(countRoot.get("name"), likePattern);
            Predicate phoneLike = cb.like(countRoot.get("phone"), likePattern);
            countPredicates.add(cb.or(nameLike, phoneLike));
        }

        if (StringUtils.isNotBlank(status)) {
            countPredicates.add(cb.equal(countRoot.get("status"), status));
        }

        if (CollectionUtils.isNotEmpty(staffIds)) {
            countPredicates.add(countRoot.get("staffId").in(staffIds));
        }

        countQuery.select(cb.count(countRoot));
        countQuery.where(countPredicates.toArray(new Predicate[0]));

        // 获取总记录数
        int totalCount = Math.toIntExact(entityManager.createQuery(countQuery).getSingleResult());

        // 转换为DTO
        List<Staff> staffList = staffEntities.stream()
                .map(staffBuilder::toStaff)
                .collect(Collectors.toList());

        // 计算总页数
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);

        return new Pagination<>(pageNum-1, pageSize, totalPages, totalCount, staffList);
    }

    /**
     * 发送员工更新事件
     *
     * @param userId
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/06 17:47:29
     */
    @Override
    public void sendUpdateEvent(UserId userId, Staff staff) {
        StaffCreateEvent event = staffBuilder.toCreateEvent(userId, staff);
        rocketMQTemplate.send(event);
    }

    @Override
    public int countByPhoneAndStaffIdNot(PhoneNumber phone, StaffId id) {
        return staffDao.countByPhoneAndDeletedAndStaffIdNot(phone.getValue(), DeleteFlag.NOT_DELETED, id.getValue());
    }

    @Override
    public void updateStaff(Staff staff) {
        StaffEntity entity = staffBuilder.toUpdateEntity(staff);
        staffDao.update(entity);
    }
}
