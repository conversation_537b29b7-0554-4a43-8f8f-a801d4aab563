package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.customer.model.CustomerId;
import com.inboyu.admin.domain.customer.model.CustomerWx;
import com.inboyu.admin.infrastructure.builder.WxCustomerBuilder;
import com.inboyu.admin.infrastructure.dao.entity.CustomerWxEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class WxCustomerBuilderImpl implements WxCustomerBuilder {
    @Override
    public CustomerWx toCustomer(CustomerWxEntity entity) {
        return CustomerWx.ofSave(
                entity.getAppId(),
                entity.getOpenId(),
                entity.getUnionId()
        );
    }

    @Override
    public CustomerWx toCustomerForUpdate(CustomerWxEntity entity) {
        return CustomerWx.of(
                entity.getAppId(),
                entity.getOpenId(),
                entity.getUnionId(),
                entity.getAuthTime(),
                CustomerId.of(entity.getCustomerId())
        );
    }

    @Override
    public CustomerWxEntity toCustomerEntify(CustomerWx customer) {
        CustomerWxEntity customerEntity = new CustomerWxEntity();
        customerEntity.setAppId(customer.getAppId());
        customerEntity.setOpenId(customer.getOpenId());
        customerEntity.setUnionId(customer.getUnionId());
        customerEntity.setAuthTime(customer.getAuthTime());
        customerEntity.setDeleted(DeleteFlag.NOT_DELETED);
        return customerEntity;
    }

    @Override
    public List<CustomerWx> toCustomertList(List<CustomerWxEntity> entityList) {
        return entityList.stream().map(this::toCustomer).collect(Collectors.toList());
    }
}
