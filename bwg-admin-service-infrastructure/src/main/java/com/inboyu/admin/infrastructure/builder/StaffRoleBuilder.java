package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.infrastructure.dao.entity.StaffRoleEntity;

import java.util.List;

/**
 * @Description: TODO
 * @Author: zhouxin
 * @Date: 2025-08-05
 */
public interface StaffRoleBuilder {
    List<StaffRoleEntity> toEntities(List<StaffDept> staffDeptList);

    StaffRoleEntity toEntity(StaffDept staffDept);

    List<StaffDept> toStaffDeptList(List<StaffRoleEntity> entityList);

    StaffDept toStaffDeptList(StaffRoleEntity staffRoleEntity);
}
