package com.inboyu.admin.infrastructure.dao;


import com.inboyu.admin.infrastructure.dao.entity.CustomerWxEntity;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;

public interface CustomerWxDao extends JpaRepository<CustomerWxEntity, Long> {

    @Query("select w from CustomerWxEntity w where w.appId = :appId and w.openId = :openId and w.deleted = :isDeleted")
    CustomerWxEntity findByAppOpenId(String appId, String openId, int isDeleted);

    @Transactional
    @Modifying
    @Query("update CustomerWxEntity set customerId = :customerId, authTime = :authTime where appId = :appId and openId = :openId")
    int update(Long customerId, String appId, String openId, LocalDateTime authTime);
}
