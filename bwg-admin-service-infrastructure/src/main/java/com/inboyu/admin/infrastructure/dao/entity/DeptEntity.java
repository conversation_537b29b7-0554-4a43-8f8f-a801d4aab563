package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_dept")
public class DeptEntity extends BaseEntity {

    @Column(name = "dept_id", nullable = false)
    private Long deptId;

    @Column(name = "parent_dept_id", nullable = false)
    private Long parentDeptId;

    @Column(name = "title", nullable = false, length = 50)
    private String title;
}
