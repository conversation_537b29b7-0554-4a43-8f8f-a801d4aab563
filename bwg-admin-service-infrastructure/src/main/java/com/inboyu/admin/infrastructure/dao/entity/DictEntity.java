package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_dict")
public class DictEntity extends BaseEntity {
    @Column(name = "code", nullable = false, length = 50)
    private String code;

    @Column(name = "type", nullable = false, length = 50)
    private String type;

    @Column(name = "title", nullable = false, length = 50)
    private String title;

    @Column(name = "icon", nullable = false)
    private String icon;

    @Column(name = "remark", nullable = false)
    private String remark;

    @Column(name = "`index`", nullable = false)
    private Integer index;
}
