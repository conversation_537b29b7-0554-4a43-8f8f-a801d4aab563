package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.infrastructure.dao.entity.DictEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月04日 10:20
 */
public interface DictBuilder {
    Dict toDict(DictEntity entity);

    DictEntity toNewEntity(Dict dict);

    DictEntity toUpdateEntity(Dict dict);

    List<Dict> toDicts(List<DictEntity> dicts);
}
