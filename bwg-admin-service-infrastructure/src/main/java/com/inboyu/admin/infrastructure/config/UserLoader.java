package com.inboyu.admin.infrastructure.config;

import com.inboyu.auth.api.AuthFeignClient;
import com.inboyu.auth.api.dto.response.UserInfoResponse;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户
 * <AUTHOR>
 */
@Component
public class UserLoader {

    @Autowired
    private FeignAccessor feignAccessor;

    /**
     * 用户信息
     * @param token
     * @param tenantId
     * @return UserInfoResponse
     */
    public UserInfoResponse getUserInfo(String token, String tenantId) {
        AuthFeignClient client = feignAccessor.get(AuthFeignClient.class);
        return client.getUserInfo(token, tenantId);
    }
}
