package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_staff_role")
public class StaffRoleEntity extends BaseEntity {
    @Column(name = "deleted", nullable = false)
    private Integer deleted;

    @Column(name = "staff_id", nullable = false)
    private Long staffId;

    @Column(name = "dept_id", nullable = false)
    private Long deptId;

    @Column(name = "include_all", nullable = false)
    private Integer includeAll;

    @Column(name = "store_ids", nullable = false, length = 500)
    private String storeIds;

    @Column(name = "role_ids", nullable = false, length = 500)
    private String roleIds;

    @Column(name = "expire_time", nullable = false)
    private LocalDateTime expireTime;

}
