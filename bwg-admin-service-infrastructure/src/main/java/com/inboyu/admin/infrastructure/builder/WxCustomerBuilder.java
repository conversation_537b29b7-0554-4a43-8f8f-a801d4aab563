package com.inboyu.admin.infrastructure.builder;


import com.inboyu.admin.domain.customer.model.CustomerWx;
import com.inboyu.admin.infrastructure.dao.entity.CustomerWxEntity;

import java.util.List;

public interface WxCustomerBuilder {

    CustomerWx toCustomer(CustomerWxEntity entify);
    CustomerWx toCustomerForUpdate(CustomerWxEntity entify);

    CustomerWxEntity toCustomerEntify(CustomerWx customer);

    List<CustomerWx> toCustomertList(List<CustomerWxEntity> entityList);

}
