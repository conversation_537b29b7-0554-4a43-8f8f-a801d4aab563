package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RoleStatus;
import com.inboyu.admin.infrastructure.builder.RoleBuilder;
import com.inboyu.admin.infrastructure.constant.RoleStatusEnum;
import com.inboyu.admin.infrastructure.dao.entity.RoleEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.util.convert.BooleanIntegerConverter;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class RoleBuilderImpl implements RoleBuilder {

    @Override
    public Role toRole(RoleEntity entity) {
        if (null == entity) {
            return null;
        }
        return Role.create(RoleId.of(entity.getRoleId()), entity.getTitle(),
                entity.getRemark(), RoleStatus.create(entity.getStatus(), RoleStatusEnum.titleFromCode(entity.getStatus())),
                BooleanIntegerConverter.intToBoolean(entity.getEnabled()));
    }

    @Override
    public RoleEntity toNewEntity(Role role) {
        if (null == role) {
            return null;
        }
        RoleEntity entity = new RoleEntity();
        entity.setRoleId(role.getId().getValue());
        entity.setTitle(role.getTitle());
        entity.setRemark(role.getRemark());
        entity.setStatus(role.getStatus().getCode());
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setEnabled(BooleanIntegerConverter.booleanToInt(role.getEnabled()));
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        return entity;
    }

    @Override
    public RoleEntity toUpdateEntity(Role role) {
        if (null == role) {
            return null;
        }
        RoleEntity entity = new RoleEntity();
        entity.setRoleId(role.getId().getValue());
        entity.setTitle(role.getTitle());
        entity.setRemark(role.getRemark());
        entity.setStatus(role.getStatus().getCode());
        entity.setEnabled(BooleanIntegerConverter.booleanToInt(role.getEnabled()));
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        return entity;
    }

    @Override
    public List<Role> toRoles(List<RoleEntity> roles) {
        if (roles == null) {
            return Collections.emptyList();
        }
        return roles.stream()
                .map(this::toRole)
                .filter(Objects::nonNull)
                .toList();
    }
}
