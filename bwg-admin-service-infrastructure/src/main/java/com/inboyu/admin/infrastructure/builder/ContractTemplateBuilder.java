package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.contracttemplate.model.ContractTemplate;
import com.inboyu.admin.infrastructure.dao.entity.ContractTemplateEntity;

/**
 * 合同模板领域对象与数据库实体互转
 * <AUTHOR>
 */
public interface ContractTemplateBuilder {
    /**
     * 将领域对象转换为数据库实体
     *
     * @param contractTemplate
     * @return {@link ContractTemplateEntity}
     */
    ContractTemplateEntity toContractTemplateEntity(ContractTemplate contractTemplate);

    /**
     * 将数据库实体转换为领域对象
     *
     * @param contractTemplateEntity
     * @return {@link ContractTemplate}
     */
    ContractTemplate toContractTemplate(ContractTemplateEntity contractTemplateEntity);
}
