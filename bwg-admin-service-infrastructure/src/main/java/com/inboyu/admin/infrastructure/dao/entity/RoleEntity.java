package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_role")
public class RoleEntity extends BaseEntity {
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    @Column(name = "title", nullable = false, length = 50)
    private String title;

    @Column(name = "remark", nullable = false)
    private String remark;

    @Column(name = "status", nullable = false, length = 50)
    private String status;

    @Column(name = "enabled", nullable = false)
    private Integer enabled;
}
