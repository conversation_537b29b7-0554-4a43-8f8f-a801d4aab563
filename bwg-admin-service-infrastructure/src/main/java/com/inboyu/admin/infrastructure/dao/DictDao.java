package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.infrastructure.dao.entity.DictEntity;
import com.inboyu.admin.infrastructure.dao.entity.RoleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 10:20
 */
public interface Dict<PERSON>ao extends JpaRepository<DictEntity, Long> {

    @Transactional
    @Modifying
    @Query("update DictEntity set icon = :#{#entity.icon}, title = :#{#entity.title}, remark = :#{#entity.remark}, index = :#{#entity.index} where code = :#{#entity.code} and type = :#{#entity.type}")
    int update(DictEntity entity);

    @Transactional
    @Modifying
    @Query("update DictEntity set deleted = :#{#entity.deleted} where code = :#{#entity.code} and type = :#{#entity.type}")
    int updateByTypeAndCode(DictEntity entity);

    /**
     * 通过字典编码查询字典项
     *
     * @param type
     * @param code
     * @param deleted
     * @return {@link DictEntity }
     * <AUTHOR> Dong
     * @date 2025/08/04 15:00:54
     */
    DictEntity findByTypeAndCodeAndDeleted(String type, String code, int deleted);

    /**
     * 根据字典项类型查询字典列表
     *
     * @param type
     * @param deleted
     * @return {@link List }<{@link RoleEntity }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:10:54
     */
    @Query("SELECT d FROM DictEntity d WHERE d.deleted = :deleted AND d.type = :type ORDER BY d.index asc")
    List<DictEntity> findDictListByType(String type, int deleted);

    /**
     * 通过字典名称统计字典项数量
     *
     * @param type
     * @param title
     * @param deleted
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/07/31 14:59:39
     */
    int countByTypeAndTitleAndDeleted(String type, String title, int deleted);
}
