package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.infrastructure.dao.entity.StaffRoleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface StaffRoleDao extends JpaRepository<StaffRoleEntity, Long> {

    /**
     * @description: 根据员工id和删除标识查询员工角色信息
     * @author: zhou<PERSON>
     * @date: 2025/8/5 16:46
     * @param: [staffId, deleted] 员工id，删除标识
     * @return: com.inboyu.admin.infrastructure.dao.entity.StaffRoleEntity
     **/
    List<StaffRoleEntity> findByStaffIdAndDeleted(Long staffId, int deleted);

    Integer countByDeptIdInAndDeleted(List<Long> deptIds, int deleted);

    /**
     * 根据员工id集合查询员工角色信息
     *
     * @param roleId
     * @param staffIds
     * @return {@link Page }<{@link StaffRoleEntity }>
     * <AUTHOR>
     * @date 2025/08/05 16:47:22
     */
    @Query("SELECT s FROM StaffRoleEntity s " +
            "WHERE s.deleted = 0 AND  (:staffIds IS NULL OR s.staffId IN (:staffIds)) " +
            "AND (:roleId IS NULL OR CONCAT(',', s.roleIds, ',') LIKE CONCAT('%,', :roleId, ',%'))")
    List<StaffRoleEntity> listByDeletedAndStaffIdsAndRoleId(String roleId, List<Long> staffIds);

    @Transactional
    @Modifying
    @Query("update StaffRoleEntity set deleted = 1 where staffId = :staffId")
    void deleteByStaffId(Long staffId);
}
