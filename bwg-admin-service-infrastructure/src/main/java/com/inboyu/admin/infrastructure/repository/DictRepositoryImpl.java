package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;
import com.inboyu.admin.domain.dict.repository.DictRepository;
import com.inboyu.admin.infrastructure.builder.DictBuilder;
import com.inboyu.admin.infrastructure.dao.DictDao;
import com.inboyu.admin.infrastructure.dao.entity.DictEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.redis.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2025年08月04日 10:20
 */
@Repository
public class DictRepositoryImpl implements DictRepository {

    // 字典code前缀
    public static final String DICT_CODE = "dict_code_";
    // 每次生成code的步长
    public static final int DELTA = 1;
    // 日期格式化
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    // 过期时间为3天
    public static final int TIMEOUT = 3 * 24 * 60 * 60;
    // code补齐长度
    public static final int LENGTH = 6;
    @Autowired
    private DictDao dictDao;

    @Autowired
    private DictBuilder dictBuilder;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public DictCode generateCode(String type) {
        // 获取当前日期（格式为 yyMMdd）
        String dateStr = LocalDate.now().format(DATE_FORMATTER);
        // 构建带日期的 Redis 键
        String key = String.format("%s:%s:%s", DICT_CODE, type, dateStr);
        String value = redisUtil.incr(key, DELTA, TIMEOUT, LENGTH);
        String code = String.format("%s%s%s", type, dateStr, value);
        return DictCode.of(code);
    }

    @Override
    public Dict insert(Dict dict) {
        DictEntity newEntity = dictBuilder.toNewEntity(dict);
        DictEntity dictEntity = dictDao.save(newEntity);
        return dictBuilder.toDict(dictEntity);
    }

    @Override
    public void delete(DictCode type, DictCode code) {
        DictEntity dictEntity = dictDao.findByTypeAndCodeAndDeleted(type.getValue(), code.getValue(), DeleteFlag.NOT_DELETED);
        dictEntity.setDeleted(DeleteFlag.DELETED);
        dictDao.updateByTypeAndCode(dictEntity);
    }

    @Override
    public Dict update(Dict dict) {
        DictEntity updateEntity = dictBuilder.toUpdateEntity(dict);
        dictDao.update(updateEntity);
        DictEntity dictEntity = dictDao.findByTypeAndCodeAndDeleted(updateEntity.getType(), updateEntity.getCode(), DeleteFlag.NOT_DELETED);
        return dictBuilder.toDict(dictEntity);
    }

    @Override
    public List<Dict> queryDictListByType(DictCode type) {
        List<DictEntity> dictListByType = dictDao.findDictListByType(type.getValue(), DeleteFlag.NOT_DELETED);
        return dictBuilder.toDicts(dictListByType);
    }

    @Override
    public int countByTypeAndTitle(String type, String title) {
        return dictDao.countByTypeAndTitleAndDeleted(type, title, DeleteFlag.NOT_DELETED);
    }

    @Override
    public Dict findByTypeAndCode(String type, String code) {
        DictEntity dictEntity = dictDao.findByTypeAndCodeAndDeleted(type, code, DeleteFlag.NOT_DELETED);
        return dictBuilder.toDict(dictEntity);
    }
}
