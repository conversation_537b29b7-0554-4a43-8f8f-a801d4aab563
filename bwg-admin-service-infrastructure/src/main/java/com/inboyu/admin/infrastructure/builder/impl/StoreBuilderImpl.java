package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.GeoPoint;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.event.StoreCreateEvent;
import com.inboyu.admin.infrastructure.builder.StoreBuilder;
import com.inboyu.admin.infrastructure.dao.entity.StoreEntity;
import com.inboyu.constant.DeleteFlag;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年08月02日 14:53
 */
@Component
public class StoreBuilderImpl implements StoreBuilder {
    @Override
    public StoreEntity toStoreEntity(Store store) {
        StoreEntity entity = new StoreEntity();
        entity.setStoreId(store.getId().getValue());
        entity.setDeptId(store.getDeptId().getValue());
        entity.setTitle(store.getTitle());
        entity.setProvinceCode(store.getProvinceCode());
        entity.setProvinceName(store.getProvinceName());
        entity.setCityCode(store.getCityCode());
        entity.setCityName(store.getCityName());
        entity.setCountyCode(store.getCountyCode());
        entity.setCountyName(store.getCountyName());
        entity.setAddress(store.getAddress());
        entity.setCoordinate(toPoint(store.getCoordinate()));
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        return entity;
    }

    /**
     * 转换为更新用的实体（设置主键ID）
     *
     * @param store
     * @param existingEntity 已存在的实体
     * @return StoreEntity
     * <AUTHOR> Dong
     * @date 2025/08/02 16:45
     */
    public StoreEntity toUpdateStoreEntity(Store store, StoreEntity existingEntity) {
        StoreEntity entity = new StoreEntity();
        // 设置主键ID，确保JPA执行update而不是insert
        entity.setId(existingEntity.getId());
        entity.setStoreId(store.getId().getValue());
        entity.setDeptId(store.getDeptId().getValue());
        entity.setTitle(store.getTitle());
        entity.setProvinceCode(store.getProvinceCode());
        entity.setProvinceName(store.getProvinceName());
        entity.setCityCode(store.getCityCode());
        entity.setCityName(store.getCityName());
        entity.setCountyCode(store.getCountyCode());
        entity.setCountyName(store.getCountyName());
        entity.setAddress(store.getAddress());
        entity.setCoordinate(toPoint(store.getCoordinate()));
        // 保持原有的创建时间
        entity.setCreateTime(existingEntity.getCreateTime());
        entity.setModifyTime(LocalDateTime.now());
        entity.setVersion(existingEntity.getVersion());
        entity.setDeleted(existingEntity.getDeleted());
        return entity;
    }

    @Override
    public Point toPoint(GeoPoint geoPoint) {
        GeometryFactory geometryFactory = new GeometryFactory();
        Coordinate coordinate = new Coordinate(
                geoPoint.getLongitude().doubleValue(),
                geoPoint.getLatitude().doubleValue()
        );
        return geometryFactory.createPoint(coordinate);
    }

    @Override
    public GeoPoint toGeoPoint(Point point) {
        if (point == null || point.getCoordinate() == null) {
            throw new IllegalArgumentException("坐标为空");
        }
        return new GeoPoint(
                BigDecimal.valueOf(point.getX()), // 经度
                BigDecimal.valueOf(point.getY())  // 纬度
        );
    }

    @Override
    public Store toStore(StoreEntity storeEntity) {
        GeoPoint geoPoint = toGeoPoint(storeEntity.getCoordinate());
        return Store.builder()
                .id(StoreId.of(storeEntity.getStoreId()))
                .deptId(DeptId.of(storeEntity.getDeptId()))
                .title(storeEntity.getTitle())
                .provinceCode(storeEntity.getProvinceCode())
                .provinceName(storeEntity.getProvinceName())
                .cityCode(storeEntity.getCityCode())
                .cityName(storeEntity.getCityName())
                .countyCode(storeEntity.getCountyCode())
                .countyName(storeEntity.getCountyName())
                .address(storeEntity.getAddress())
                .coordinate(geoPoint)
                .build();
    }

    @Override
    public StoreCreateEvent toCreateEvent(Store store) {
        StoreCreateEvent event = new StoreCreateEvent();
        event.setStoreId(store.getId().getValue());
//        event.setTenantId(SystemContextUtils.getTenantId());
        return event;
    }


}
