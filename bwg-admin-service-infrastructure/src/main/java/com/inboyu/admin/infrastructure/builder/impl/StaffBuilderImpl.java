package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.staff.model.*;
import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.admin.event.StoreCreateEvent;
import com.inboyu.admin.infrastructure.builder.StaffBuilder;
import com.inboyu.admin.infrastructure.constant.StaffStatusEnum;
import com.inboyu.admin.infrastructure.dao.entity.StaffEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.context.base.utils.SystemContextUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 14:02
 */
@Component
public class StaffBuilderImpl implements StaffBuilder {

    @Override
    public StaffEntity toStaffEntity(Staff staff) {
        if (staff == null) {
            return null;
        }
        StaffEntity entity = new StaffEntity();
        entity.setStaffId(staff.getId().getValue());
        entity.setUserId(staff.getUserId().getValue());
        entity.setName(staff.getName());
        entity.setPhone(staff.getPhone().getValue());
        entity.setStatus(staff.getStatus().getCode());
        // 设置基础字段
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        return entity;
    }

    @Override
    public Staff toStaff(StaffEntity entity) {
        if (entity == null) {
            return null;
        }
        return Staff.builder()
                .id(StaffId.of(entity.getStaffId()))
                .userId(UserId.of(entity.getUserId()))
                .name(entity.getName())
                .phone(PhoneNumber.of(entity.getPhone()))
                .status(StaffStatus.create(entity.getStatus(), StaffStatusEnum.titleFromCode(entity.getStatus())))
                .createTime(entity.getCreateTime())
                .build();
    }

    /**
     * 将领域对象转换为消息实体
     *
     * @param staff
     * @return {@link StoreCreateEvent }
     * <AUTHOR> Dong
     * @date 2025/08/05 16:53:45
     */
    @Override
    public StaffCreateEvent toCreateEvent(UserId userId, Staff staff) {
        StaffCreateEvent event = new StaffCreateEvent();
        event.setTenantId(Long.valueOf(SystemContextUtils.getTenantId()));
        event.setStaffId(staff.getId().getValue());
        event.setUserId(staff.getUserId().getValue());
        event.setStatus(staff.getStatus().getCode());
        if (null != userId) {
            event.setUserId(userId.getValue());
        }
        return event;
    }

    @Override
    public StaffEntity toUpdateEntity(Staff staff) {
        if (staff == null) {
            return null;
        }
        StaffEntity entity = new StaffEntity();
        entity.setStaffId(staff.getId().getValue());
        entity.setUserId(staff.getUserId().getValue());
        entity.setName(staff.getName());
        entity.setPhone(staff.getPhone().getValue());
        entity.setStatus(staff.getStatus().getCode());
        // 设置基础字段
        entity.setModifyTime(LocalDateTime.now());
        return entity;
    }
}
