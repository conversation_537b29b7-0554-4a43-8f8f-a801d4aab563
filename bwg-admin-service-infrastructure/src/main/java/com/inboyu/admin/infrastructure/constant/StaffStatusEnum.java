package com.inboyu.admin.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 员工状态枚举
 *
 * <AUTHOR> Dong
 * @date 2025年08月05日 13:43
 */
@AllArgsConstructor
@Getter
public enum StaffStatusEnum {
    ENABLED("status.enabled", "启用"),
    DISABLED("status.disabled", "停用");
    private final String code;
    private final String title;

    /**
     * @description: 根据code获取title
     * @author: <PERSON><PERSON><PERSON>
     * @date: 2025/8/5 15:28
     * @param: [code]
     * @return: java.lang.String
     **/
    public static String titleFromCode(String code) {
        for (StaffStatusEnum status : StaffStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getTitle();
            }
        }
        return null;
    }
}
