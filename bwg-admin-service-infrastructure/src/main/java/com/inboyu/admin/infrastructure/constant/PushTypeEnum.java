package com.inboyu.admin.infrastructure.constant;

import lombok.Getter;

/**
 * 推送方式枚举
 * <AUTHOR>
 */
@Getter
public enum PushTypeEnum {

    PUSH_FORWARD_MONTH("push_type.push_forward_month", "顺推月"),;

    private final String code;
    private final String title;

    PushTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static PushTypeEnum getByCode(String code) {
        for (PushTypeEnum pushTypeEnum : PushTypeEnum.values()) {
            if (pushTypeEnum.getCode().equals(code)) {
                return pushTypeEnum;
            }
        }
        return null;
    }
}