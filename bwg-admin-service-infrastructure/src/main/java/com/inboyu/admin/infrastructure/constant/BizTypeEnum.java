package com.inboyu.admin.infrastructure.constant;

import lombok.Getter;

/**
 * 业务类型枚举
 * <AUTHOR>
 */
@Getter
public enum BizTypeEnum {

    PLATFORM("biz_type.platform", "平台"),
    TENANT("biz_type.tenant","运营商");

    private final String code;
    private final String title;

    BizTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static BizTypeEnum getByCode(String code) {
        for (BizTypeEnum operationTypeEnum : BizTypeEnum.values()) {
            if (operationTypeEnum.getCode().equals(code)) {
                return operationTypeEnum;
            }
        }
        return null;
    }
}
