package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.operationlog.model.OperationId;
import com.inboyu.admin.domain.operationlog.model.OperationLog;
import com.inboyu.admin.domain.operationlog.repository.OperationLogRepository;
import com.inboyu.admin.infrastructure.builder.OperationLogBuilder;
import com.inboyu.admin.infrastructure.constant.CommonFiledEnum;
import com.inboyu.admin.infrastructure.constant.OperationLogFiledEnum;
import com.inboyu.admin.infrastructure.dao.OperationLogDao;
import com.inboyu.admin.infrastructure.dao.entity.OperationLogEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作日志数仓实现
 * <AUTHOR>
 */
@Repository
public class OperationLogRepositoryImpl implements OperationLogRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private OperationLogDao operationLogDao;

    @Autowired
    private OperationLogBuilder operationLogBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public OperationId generateId() {
        return OperationId.of(snowflakeIdGenerator.nextId());
    }

    @Override
    public OperationLog collect(OperationLog operationLog) {
        OperationLogEntity operationLogEntity = operationLogBuilder.toOperationLogEntity(operationLog);
        OperationLogEntity save = operationLogDao.save(operationLogEntity);
        return operationLogBuilder.toOperationLog(save);
    }

    @Override
    public Pagination<OperationLog> page(String operator, String operationType, Boolean success, String operationTimeStart, String operationTimeEnd, Integer pageNum, Integer pageSize) {
        // 创建分页参数
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);

        // 创建Criteria查询
        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<OperationLogEntity> query = cb.createQuery(OperationLogEntity.class);
        Root<OperationLogEntity> root = query.from(OperationLogEntity.class);

        List<Predicate> predicates = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (operator != null) {
            predicates.add(cb.like(root.get(OperationLogFiledEnum.OPERATOR.getTitle()), operator));
        }
        if (operationType != null) {
            predicates.add(cb.equal(root.get(OperationLogFiledEnum.OPERATION_TYPE.getTitle()), operationType));
        }
        if (success != null) {
            predicates.add(cb.equal(root.get(OperationLogFiledEnum.SUCCESS.getTitle()), success));
        }
        if (operationTimeStart != null) {
            LocalDate parse = LocalDate.parse(operationTimeStart, dateTimeFormatter);
            predicates.add(cb.greaterThanOrEqualTo(root.get(OperationLogFiledEnum.OPERATION_TIME.getTitle()), parse.atTime(0,0,0)));
        }
        if (operationTimeEnd != null) {
            LocalDate parse = LocalDate.parse(operationTimeEnd, dateTimeFormatter);
            predicates.add(cb.lessThanOrEqualTo(root.get(OperationLogFiledEnum.OPERATION_TIME.getTitle()), parse.atTime(23,59,59)));
        }
        //剔除掉deleted为1的数据
        predicates.add(cb.equal(root.get(CommonFiledEnum.DELETED.getTitle()), DeleteFlag.NOT_DELETED));

        // 组合查询条件
        query.where(cb.and(predicates.toArray(new Predicate[0])));
        //按照数据创建时间倒序排序
        query.orderBy(cb.desc(root.get(CommonFiledEnum.CREATE_TIME.getTitle())));

        // 使用相同的root对象执行分页查询
        List<OperationLogEntity> operationLogEntities = entityManager.createQuery(query)
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize())
                .getResultList();

        // 使用单独的查询获取总数（避免共享root）
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<OperationLogEntity> countRoot = countQuery.from(OperationLogEntity.class);

        // 重新构建计数查询的条件
        List<Predicate> countPredicates = new ArrayList<>();
        if (operator != null) {
            countPredicates.add(cb.like(countRoot.get(OperationLogFiledEnum.OPERATOR.getTitle()), operator));
        }
        if (operationType != null) {
            countPredicates.add(cb.equal(countRoot.get(OperationLogFiledEnum.OPERATION_TYPE.getTitle()), operationType));
        }
        if (success != null) {
            countPredicates.add(cb.equal(countRoot.get(OperationLogFiledEnum.SUCCESS.getTitle()), success));
        }
        if (operationTimeStart != null) {
            LocalDate parse = LocalDate.parse(operationTimeStart, dateTimeFormatter);
            countPredicates.add(cb.greaterThanOrEqualTo(countRoot.get(OperationLogFiledEnum.OPERATION_TIME.getTitle()), parse.atTime(0, 0, 0)));
        }
        if (operationTimeEnd != null) {
            LocalDate parse = LocalDate.parse(operationTimeEnd, dateTimeFormatter);
            countPredicates.add(cb.lessThanOrEqualTo(countRoot.get(OperationLogFiledEnum.OPERATION_TIME.getTitle()), parse.atTime(23,59,59)));
        }
        //剔除掉deleted为1的数据
        countPredicates.add(cb.equal(countRoot.get(CommonFiledEnum.DELETED.getTitle()), DeleteFlag.NOT_DELETED));

        // 组合计数查询条件
        countQuery.where(cb.and(countPredicates.toArray(new Predicate[0])));
        countQuery.select(cb.count(countRoot));

        // 获取总记录数
        int totalCount = Math.toIntExact(entityManager.createQuery(countQuery).getSingleResult());

        // 转换为DTO
        List<OperationLog> operationLogList = operationLogEntities.stream()
                .map(operationLogBuilder::toOperationLog)
                .collect(Collectors.toList());

        // 计算总页数
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);

        // 返回分页结果
        return new Pagination<>(pageNum, pageSize, totalPages, totalCount, operationLogList);
    }
}
