package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_role_scope")
public class RoleScopeEntity extends BaseEntity {
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    @Column(name = "group_code", nullable = false, length = 100)
    private String groupCode;

    @Column(name = "perssion", nullable = false, length = 100)
    private String perssion;

    @Column(name = "hidden", nullable = false)
    private Integer hidden;

    @Column(name = "disabled", nullable = false)
    private Integer disabled;
}
