package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.operationlog.model.OperationLog;
import com.inboyu.admin.infrastructure.dao.entity.OperationLogEntity;

/**
 * 操作日志领域对象与数据库实体互转
 * <AUTHOR>
 */
public interface OperationLogBuilder {
    /**
     * 将领域对象转换为数据库实体
     *
     * @param operationLog
     * @return {@link OperationLogEntity}
     */
    OperationLogEntity toOperationLogEntity(OperationLog operationLog);

    /**
     * 将数据库实体转换为领域对象
     *
     * @param operationLogEntity
     * @return {@link OperationLog}
     */
    OperationLog toOperationLog(OperationLogEntity operationLogEntity);
}
