package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.infrastructure.dao.entity.RoleScopeEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月31日 16:15
 */
public interface RoleScopeBuilder {
    List<RoleScopeEntity> toEntities(List<RolePermission> rolePermissions);

    List<RolePermission> toRolePermission(List<RoleScopeEntity> roleScopeEntities);
}
