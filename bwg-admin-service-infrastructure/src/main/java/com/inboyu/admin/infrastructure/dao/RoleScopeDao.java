package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.infrastructure.dao.entity.RoleScopeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface RoleScopeDao extends JpaRepository<RoleScopeEntity, Long> {
    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @param deleted
     * @return {@link List }<{@link RoleScopeEntity }>
     * <AUTHOR>
     * @date 2025/07/31 17:12:01
     */
    List<RoleScopeEntity> findByRoleIdAndDeleted(Long roleId, int deleted);

    /**
     * 根据角色id删除权限列表
     *
     * @param roleId
     * @return void
     * <AUTHOR>
     * @date 2025/07/31 17:12:01
     */
    @Transactional
    @Modifying
    @Query("update RoleScopeEntity set deleted = 1, version = version + 1 where roleId = :roleId")
    void deleteByRoleId(Long roleId);

    /**
     * 通过角色id查询角色权限
     *
     * @param list
     * @param deleted
     * @return {@link List }<{@link RoleScopeEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/01 09:53:24
     */
    List<RoleScopeEntity> findByRoleIdInAndDeleted(List<Long> list, Integer deleted);
}
