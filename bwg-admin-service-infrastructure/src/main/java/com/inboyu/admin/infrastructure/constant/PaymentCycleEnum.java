package com.inboyu.admin.infrastructure.constant;

import lombok.Getter;

/**
 * 缴费周期枚举
 * <AUTHOR>
 */
@Getter
public enum PaymentCycleEnum {

    MONTH("payment_cycle.month", "月"),
    QUARTER("payment_cycle.quarter","季"),
    HALF_YEAR("payment_cycle.half_year","半年"),
    YEAR("payment_cycle.year","年");

    private final String code;
    private final String title;

    PaymentCycleEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static PaymentCycleEnum getByCode(String code) {
        for (PaymentCycleEnum paymentCycleEnum : PaymentCycleEnum.values()) {
            if (paymentCycleEnum.getCode().equals(code)) {
                return paymentCycleEnum;
            }
        }
        return null;
    }
}