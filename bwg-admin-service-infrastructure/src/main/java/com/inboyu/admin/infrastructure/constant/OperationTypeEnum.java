package com.inboyu.admin.infrastructure.constant;

import lombok.Getter;

/**
 * 操作类型枚举
 * <AUTHOR>
 */
@Getter
public enum OperationTypeEnum {

    UPDATE_CONTRACT_TEMPLATE("operation_type.update_contract_template", "修改合同模板"),;

    private final String code;
    private final String title;

    OperationTypeEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public static OperationTypeEnum getByCode(String code) {
        for (OperationTypeEnum operationTypeEnum : OperationTypeEnum.values()) {
            if (operationTypeEnum.getCode().equals(code)) {
                return operationTypeEnum;
            }
        }
        return null;
    }
}
