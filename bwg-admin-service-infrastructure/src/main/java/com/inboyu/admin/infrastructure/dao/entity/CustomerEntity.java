package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.annotation.Encrypted;
import com.inboyu.spring.cloud.starter.jpa.converter.SensitiveConverter;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

/**
 * 租赁人信息表
 * 对应数据库表：t_customer
 */
@Data
@Entity
@Table(
        name = "t_customer",
        indexes = {
                @Index(name = "idx_customer_id", columnList = "customer_id"),
                @Index(name = "idx_name", columnList = "name"),
                @Index(name = "idx_mobile", columnList = "mobile")
        }
)
@Comment("租赁人")
public class CustomerEntity extends BaseEntity {

    /**
     * 租赁人id
     */
    @Column(name = "customer_id", nullable = false)
    @Comment("租赁人id")
    private Long customerId;

    /**
     * 租赁人姓名
     */
    @Column(name = "name", nullable = false)
    @Comment("租赁人姓名")
    private String name;

    /**
     * 租赁人手机号
     */
    @Column(name = "mobile", nullable = false)
    @Comment("租赁人手机号")
    @Encrypted
    @Convert(converter = SensitiveConverter.class) //自动加解密敏感数据
    private String mobile;

}
