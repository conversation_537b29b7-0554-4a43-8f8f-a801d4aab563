package com.inboyu.admin.infrastructure.builder;


import com.inboyu.admin.domain.customer.model.Customer;
import com.inboyu.admin.domain.customer.model.CustomerReal;
import com.inboyu.admin.infrastructure.dao.entity.CustomerEntity;
import com.inboyu.admin.infrastructure.dao.entity.CustomerRealEntity;

import java.util.List;

public interface CustomerBuilder {

    Customer toCustomer(CustomerEntity entify);

    CustomerEntity toCustomerEntify(Customer customer);

    List<Customer> toCustomertList(List<CustomerEntity> entityList);

    CustomerRealEntity toCustomerRealEntify(CustomerReal customerReal);

    CustomerReal toCustomerReal(CustomerRealEntity saveEntify,CustomerReal customerReal);
}
