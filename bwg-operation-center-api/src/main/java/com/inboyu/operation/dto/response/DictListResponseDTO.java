package com.inboyu.operation.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 字典项列表DTO
 * 用于传输字典项集合数据
 */
@Data
@Schema(description = "字典项列表数据")
public class DictListResponseDTO {
    /**
     * 字典项数组
     */
    @Schema(description = "字典项集合", example = "[{\"code\":\"status_1\",\"title\":\"启用\",\"icon\":\"check\",\"remark\":\"正常使用状态\"}]")
    private List<DictItemDTO> dict;

    /**
     * 字典项详情DTO
     * 包含单个字典项的编码、名称等信息
     */
    @Data
    @Schema(description = "字典项详情")
    public static class DictItemDTO {

        /**
         * 字典项编码
         */
        @Schema(description = "字典项唯一编码", example = "status_0")
        private String code;

        /**
         * 字典项名称
         */
        @Schema(description = "字典项显示名称", example = "禁用")
        private String title;

        /**
         * 图标
         */
        @Schema(description = "字典项对应的图标标识", example = "close", nullable = true)
        private String icon;

        /**
         * 说明
         */
        @Schema(description = "字典项的补充说明", example = "禁止使用状态", nullable = true)
        private String remark;
    }
}
