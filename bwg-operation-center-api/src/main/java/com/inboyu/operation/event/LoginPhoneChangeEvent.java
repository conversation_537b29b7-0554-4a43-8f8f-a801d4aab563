package com.inboyu.operation.event;

import com.inboyu.operation.constant.OperationRocketMqTopicConstant;
import com.inboyu.spring.cloud.starter.common.event.MessageEvent;
import lombok.Data;

@Data
public class LoginPhoneChangeEvent extends MessageEvent {

    /**
     * 新手机号
     */
    private String newPhone;

    /**
     * 新手机号对应的姓名
     */
    private String newName;

    private Long newUserId;

    /**
     * 变更前的手机号，如果之前并没有会是空
     */
    private String oldPhone;

    private Long oldUserId;

    public LoginPhoneChangeEvent() {
        super.setTopic(OperationRocketMqTopicConstant.TOPIC_BWG_TENANT);
        super.setTag(OperationRocketMqTopicConstant.TAG_LOGIN_PHONE_CHANGED);
    }
}
