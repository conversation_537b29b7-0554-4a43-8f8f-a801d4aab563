package com.inboyu.user.test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import com.inboyu.user.UserCoreApplication;
import com.inboyu.user.api.AccountFeignClient;
import com.inboyu.user.app.application.UserAppService;
import com.inboyu.user.dto.UserResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest(classes = UserCoreApplication.class)
public class UserAppServiceTest {

    private static final String phone = "***********";

    @Autowired
    private UserAppService userAppService;

    @Autowired
    private FeignAccessor feignAccessor;

    @Test
    void testCreateUser() {
        UserResponse response = this.userAppService.createUserByPhoneAccount(phone);
        assertNotNull(response);
    }

    @Test
    void testGetUser() {
        // 执行测试
        UserResponse response = this.userAppService.getUserByPhoneAccount(phone);
        log.info("response: {}", response);
    }

    @Test
    void testGetUserByUserId() {
        // 执行测试
        UserResponse response = this.userAppService.getUserByUserId(1L);
        log.info("response: {}", response);
    }

    @Test
    void testFeignClient() {
        AccountFeignClient client = feignAccessor.get(AccountFeignClient.class);
        UserResponse response = client.getUserByPhone(phone);
         log.info("response: {}", response);
    }

}
