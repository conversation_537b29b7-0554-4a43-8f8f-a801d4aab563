package com.inboyu.user.app.application.impl;

import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;
import com.inboyu.spring.cloud.starter.redis.utils.RedisUtil;
import com.inboyu.user.app.application.UserAppService;
import com.inboyu.user.app.application.converter.UserResponseConvert;
import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.AccountId;
import com.inboyu.user.domain.model.PhoneNumber;
import com.inboyu.user.domain.model.User;
import com.inboyu.user.domain.model.UserId;
import com.inboyu.user.domain.repository.AccountRepository;
import com.inboyu.user.domain.repository.UserRepository;
import com.inboyu.user.dto.UserResponse;
import com.inboyu.user.exception.ResponseCode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 业务逻辑编排实现
 * 
 * <AUTHOR>
 */
@Service
public class UserAppServiceImpl implements UserAppService {

	private static final String FILTER_ACCOUNT_TYPE = "filter_phone_%s";

	private static final String FILTER_USER_TYPE = "filter_user_%s";

	private static final int FILTE_ACCOUNT_TIMEOUT = 7200;

	@Autowired
	private AccountRepository accountRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private RedisUtil redisUtil;

	@Override
	@Transactional(readOnly = true)
	public UserResponse getUserByPhoneAccount(String phone) {
		PhoneNumber phoneNumber = PhoneNumber.of(phone);
		// 查询布隆过滤
		if (redisUtil.hasKey(String.format(FILTER_ACCOUNT_TYPE, phoneNumber.getValue()))) {
			throw new AppException(ResponseCode.USER_NOT_EXIST);
		}
		// 查询账号（优先走缓存）
		Account account = accountRepository.getPhoneAccount(phoneNumber)
				.orElseThrow(() -> {
					// 加入到布隆过滤器
					redisUtil.set(String.format(FILTER_ACCOUNT_TYPE, phoneNumber.getValue()), "",
							FILTE_ACCOUNT_TIMEOUT);
					throw new AppException(ResponseCode.USER_NOT_EXIST);
				});
		// 查询用户（优先走缓存）
		User user = userRepository.getAccountUser(account)
				.orElseThrow(() -> new AppException(ResponseCode.USER_NOT_EXIST));
		// 删除布隆过滤
		redisUtil.del(String.format(FILTER_ACCOUNT_TYPE, phoneNumber.getValue()));
		// 转换为响应对象
		return UserResponseConvert.convert(user, account);
	}

	@Override
	@Transactional
	@DistributedLock(key = "#phone", transType = "locker_phone_")
	public UserResponse createUserByPhoneAccount(String phone) {
		try {
			PhoneNumber phoneNumber = PhoneNumber.of(phone);
			// 1. 查询账号（带缓存）
			Account account = accountRepository.getPhoneAccount(phoneNumber).orElse(null);
			if (account != null) {
				// 2. 账号已存在，直接返回用户信息（带缓存）
				User user = userRepository.getAccountUser(account)
						.orElseThrow(() -> new AppException(
								ResponseCode.USER_NOT_EXIST));
				return UserResponseConvert.convert(user, account);
			}
			// 3. 账号不存在，创建新账号
			UserId newUserId = userRepository.generateId()
					.orElseThrow(() -> new AppException(ResponseCode.USER_NOT_EXIST));
			User newUser = User.create(newUserId);
			AccountId newAccountId = accountRepository.generateId()
					.orElseThrow(() -> new AppException(ResponseCode.USER_NOT_EXIST));
			Account newAccount = Account.create(newUserId, newAccountId, phoneNumber);
			accountRepository.save(newAccount);
			// 4. 创建新用户并关联账号
			newUser.addPhoneAccount(newAccount);
			userRepository.save(newUser);
			// 5. 返回用户
			// 6. 删除2个布隆过滤器
			redisUtil.del(String.format(FILTER_USER_TYPE, newUserId.getValue()));
			redisUtil.del(String.format(FILTER_ACCOUNT_TYPE, phoneNumber.getValue()));
			return UserResponseConvert.convert(newUser, newAccount);
		} finally {

		}
	}

	@Override
	@Transactional(readOnly = true)
	public UserResponse getUserByUserId(Long userId) {
		UserId usrId = UserId.of(userId);
		// 布隆过滤器
		if (redisUtil.hasKey(String.format(FILTER_USER_TYPE, usrId.getValue()))) {
			throw new AppException(ResponseCode.USER_NOT_EXIST);
		}
		// 1. 查询用户（带缓存）
		User user = userRepository.get(usrId)
				.orElseThrow(() -> {
					//加入布隆过滤器
					redisUtil.set(String.format(FILTER_USER_TYPE, usrId.getValue()), "",FILTE_ACCOUNT_TIMEOUT);
					throw new AppException(ResponseCode.USER_NOT_EXIST);
				});
		// 2. 查询关联账号（带缓存）
		Account account = accountRepository.getAccountByUserId(usrId)
				.orElseThrow(() -> new AppException(ResponseCode.USER_NOT_EXIST));
		// 3. 转换为响应对象
		// 删除布隆过滤
		redisUtil.del(String.format(FILTER_USER_TYPE, usrId.getValue()));
		return UserResponseConvert.convert(user, account);
	}

}
