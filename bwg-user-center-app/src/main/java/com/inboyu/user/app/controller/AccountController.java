package com.inboyu.user.app.controller;

import com.inboyu.user.api.AccountFeignClient;
import com.inboyu.user.app.application.UserAppService;
import com.inboyu.user.dto.CreatePhoneAccountRequest;
import com.inboyu.user.dto.UserResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * 账号管理 api 接口
 * <AUTHOR>
 */
@Tag(name="账号管理")
@RestController
@RequestMapping("/api/v1/accounts")
public class AccountController implements AccountFeignClient {

    private final UserAppService userAppService;

    public AccountController(final UserAppService userAppService) {
        this.userAppService = userAppService;
    }

    @Operation(summary = "手机号查询用户")
    @GetMapping("")
    @Override
    public UserResponse getUserByPhone(@RequestParam("phone") String phone) {
        return this.userAppService.getUserByPhoneAccount(phone);
    }

    @Operation(summary = "手机号创建用户")
    @PostMapping("")
    @Override
    public UserResponse createUserByPhone(@RequestBody CreatePhoneAccountRequest request) {
        return this.userAppService.createUserByPhoneAccount(request.getPhone());
    }
}
