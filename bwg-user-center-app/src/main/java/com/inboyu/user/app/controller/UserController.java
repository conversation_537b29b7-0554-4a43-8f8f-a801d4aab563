package com.inboyu.user.app.controller;

import com.inboyu.user.api.UserFeignClient;
import com.inboyu.user.app.application.UserAppService;
import com.inboyu.user.dto.UserResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户管理 api 接口
 * <AUTHOR>
 */
@Tag(name="用户管理")
@RestController
@RequestMapping("/api/v1/users")
public class UserController  implements UserFeignClient {

    private final UserAppService userAppService;

    public UserController(final UserAppService userAppService) {
        this.userAppService = userAppService;
    }

    @Operation(summary = "根据用户标识获取用户")
    @GetMapping("")
    @Override
    public UserResponse getUserByUserId(Long userId) {
        return userAppService.getUserByUserId(userId);
    }
}
