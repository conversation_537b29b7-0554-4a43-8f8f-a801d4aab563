package com.inboyu.user.app.application.converter;

import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.User;
import com.inboyu.user.dto.UserResponse;

/**
 * 用户信息转化器
 * <AUTHOR>
 */
public class UserResponseConvert {

    public static UserResponse convert(User user, Account account) {
        UserResponse response = new UserResponse();
        response.setUserId(user.getUserId().getValue());
        response.setNickname(user.getNickname());
        response.setAvatar(user.getAvatar());
        response.setPhone(account.getIdentifier());
        return response;
    }
}
