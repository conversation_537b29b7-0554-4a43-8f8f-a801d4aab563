package com.inboyu.user.app.application;

import com.inboyu.user.dto.UserResponse;

/**
 * 用户逻辑编排
 * <AUTHOR>
 */
public interface UserAppService {

    /**
     * 根据手机号获取用户
     * @param phone 手机号
     * @return 用户信息
     */
    UserResponse getUserByPhoneAccount(String phone);

    /**
     * 根据手机号创建用户
     * @param phone 手机号
     * @return 用户信息
     */
    UserResponse createUserByPhoneAccount(String phone);

    /**
     * 根据用户标识获取用户
     * @param userId 用户标识
     * @return 用户信息
     */
    UserResponse getUserByUserId(Long userId);
}
