package com.inboyu.admin.app.dto.request.resource;

import com.inboyu.admin.domain.store.model.StoreId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月04日 15:46
 */
@Data
@Builder
public class ResourceRequestDTO {

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "门店不能为空")
    private StoreId storeId;

    @Schema(description = "资源类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "资源类型不能为空")
    private ResourceTypeDTO resourceTypeDTO;

    @Schema(description = "业务标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务标识不能为空")
    private BusinessTypeDTO businessTypeDTO;
}
