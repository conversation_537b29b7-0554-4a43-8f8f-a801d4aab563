package com.inboyu.admin.app.application.impl;

import com.inboyu.admin.app.application.DictAppService;
import com.inboyu.admin.app.dto.converter.DictAppConverter;
import com.inboyu.admin.app.dto.request.dict.DictCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dict.DictUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dict.DictCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictListResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictUpdateResponseDTO;
import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;
import com.inboyu.admin.domain.dict.model.DictCreationResult;
import com.inboyu.admin.domain.dict.model.DictUpdateResult;
import com.inboyu.admin.domain.dict.service.DictDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年08月04日 14:58
 */
@Service
public class DictAppServiceImpl implements DictAppService {

    @Autowired
    private DictDomainService dictDomainService;

    @Autowired
    private DictAppConverter dictAppConverter;

    @Override
    public DictCreateResponseDTO createDict(String type, DictCreateRequestDTO dto) {
        DictCode dictCode = dictDomainService.generateCode(type);
        Dict dict = Dict.create(dictCode, DictCode.of(type), dto.getTitle(), dto.getIcon(), dto.getRemark(), dto.getIndex());
        DictCreationResult result = dictDomainService.createDict(dict);
        return dictAppConverter.toDictCreateResponseDTO(result);
    }

    @Override
    public void deleteDict(String type, String code) {
        dictDomainService.deleteDict(DictCode.of(type), DictCode.of(code));
    }

    @Override
    public DictUpdateResponseDTO updateDict(String type, String code, DictUpdateRequestDTO dto) {
        Dict dict = Dict.create(DictCode.of(code),DictCode.of(type), dto.getTitle(), dto.getIcon(), dto.getRemark(), dto.getIndex());
        DictUpdateResult result = dictDomainService.updateDict(dict);
        return dictAppConverter.toDictUpdateResponseDTO(result);
    }

    @Override
    public DictListResponseDTO listDict(String type) {
        return dictAppConverter.toDictListResponseDTO(dictDomainService.queryDictListByType(DictCode.of(type)));
    }
}
