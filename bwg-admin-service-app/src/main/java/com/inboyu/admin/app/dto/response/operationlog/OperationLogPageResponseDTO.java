package com.inboyu.admin.app.dto.response.operationlog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作日志查询响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogPageResponseDTO {

    @Schema(title = "员工ID")
    private String staffId;
    @Schema(title = "操作员")
    private String operator;
    @Schema(title = "操作时间")
    private String operationTime;
    @Schema(title = "操作类型")
    private String operationType;
    @Schema(title = "标题")
    private String title;
    @Schema(title = "内容")
    private String content;
    @Schema(title = "附件名称")
    private String attachmentName;
    @Schema(title = "附件地址")
    private String attachmentUrl;
    @Schema(title = "是否成功")
    private Boolean success;
    @Schema(title = "失败原因")
    private String failReason;
    @Schema(title = "业务类型")
    private String bizType;
}
