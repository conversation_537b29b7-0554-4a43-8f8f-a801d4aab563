package com.inboyu.admin.app.dto.request.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月04日 15:46
 */
@Data
public class DictCreateRequestDTO {

    @Schema(description = "字典值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "字典值不能为空")
    private String title;

    @Schema(description = "字典图标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "字典图标不能为空")
    private String icon;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer index;
}
