package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.request.operationlog.OperationLogCollectRequestDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogCollectResponseDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogPageResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

/**
 * 操作日志应用服务
 * <AUTHOR>
 */
public interface OperationLogAppService {
    /**
     * 操作日志采集
     *
     * @param dto
     * @return {@link OperationLogCollectResponseDTO}
     */
    OperationLogCollectResponseDTO collect(OperationLogCollectRequestDTO dto);

    /**
     * 操作日志查询
     *
     * @param operator 操作人（可选）
     * @param operationType 操作类型（可选）
     * @param success 操作是否成功（可选，true/false）
     * @param operationTimeStart 操作开始时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     * @param operationTimeEnd 操作结束时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     * @param pageNum 页码（默认1）
     * @param pageSize 每页条数（默认10）
     * @return 分页结果
     */
    Pagination<OperationLogPageResponseDTO> page(String operator, String operationType, Boolean success, String operationTimeStart, String operationTimeEnd, Integer pageNum, Integer pageSize);
}
