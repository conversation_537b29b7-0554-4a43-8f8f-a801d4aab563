package com.inboyu.admin.app.application;


import com.inboyu.admin.app.dto.request.customer.CustomerRequestDTO;
import com.inboyu.admin.app.dto.response.customer.CustomerResponseDTO;

import java.util.List;

public interface CustomerAppService {

    CustomerResponseDTO addCustomer(Long tenantId, CustomerRequestDTO customerRequestDTO);

    List<CustomerResponseDTO> getCustomerList(Long tenantId, String keywords);

    CustomerResponseDTO getCustomerByPhone(String phone);
    CustomerResponseDTO getByCustomerId(String customerId);
}
