package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.RoleAppService;
import com.inboyu.admin.app.dto.common.role.RoleDTO;
import com.inboyu.admin.app.dto.request.role.RoleCreateRequestDTO;
import com.inboyu.admin.app.dto.request.role.RoleUpdateRequestDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/30 16:52:26
 */
@RestController
@RequestMapping("/api/v1/roles")
@Tag(name = "角色控制器")
public class RoleController {

    @Autowired
    private RoleAppService roleAppService;

    @PostMapping
    @Operation(summary = "添加角色")
    public RoleDTO createRole(@RequestBody @Valid RoleCreateRequestDTO dto) {
        return roleAppService.createRole(dto);
    }

    @GetMapping("/{roleId}")
    @Operation(summary = "查询角色详情")
    public RoleDTO getRole(@Schema(description = "角色id") @PathVariable String roleId) {
        return roleAppService.getRole(Long.valueOf(roleId));
    }

    @PostMapping("/{roleId}")
    @Operation(summary = "编辑角色")
    public RoleDTO updateRole(@Schema(description = "角色id") @PathVariable String roleId, @RequestBody @Valid RoleUpdateRequestDTO dto) {
        return roleAppService.updateRole(Long.valueOf(roleId), dto);
    }

    /**
     * 列出角色
     *
     * @param title
     * @return RoleDTO 角色信息
     * <AUTHOR> Dong
     * @date 2025/07/31 20:48:34
     */
    @GetMapping
    @Operation(summary = "列出角色")
    public List<RoleDTO> listRoles(@Schema(description = "角色名称(支持模糊)") @RequestParam(required = false) String title,
                                   @Schema(description = "状态") @RequestParam(required = false) String status) {
        return roleAppService.listRoles(status, title);
    }
}
