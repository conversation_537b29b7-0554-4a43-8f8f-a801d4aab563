package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.common.role.RoleDTO;
import com.inboyu.admin.app.dto.request.role.RoleCreateRequestDTO;
import com.inboyu.admin.app.dto.request.role.RoleUpdateRequestDTO;

import java.util.List;

public interface RoleAppService {

    RoleDTO queryRolesInfo(Long roleId);

    /**
     * 添加角色
     *
     * @param dto
     * @return {@link RoleDTO }
     * <AUTHOR>
     * @date 2025/07/31 14:07:16
     */
    RoleDTO createRole(RoleCreateRequestDTO dto);

    /**
     * 查询角色详情
     *
     * @param roleId
     * @return {@link RoleDTO }
     * <AUTHOR>
     * @date 2025/07/31 16:56:09
     */
    RoleDTO getRole(Long roleId);

    /**
     * 编辑角色
     *
     * @param roleId
     * @param dto
     * @return {@link RoleDTO }
     * <AUTHOR>
     * @date 2025/07/31 18:02:20
     */
    RoleDTO updateRole(Long roleId, RoleUpdateRequestDTO dto);

    /**
     * 列出角色
     *
     * @param status
     * @param title
     * @return {@link List }<{@link RoleDTO }>
     * <AUTHOR>
     * @date 2025/07/31 20:53:27
     */
    List<RoleDTO> listRoles(String status, String title);
}
