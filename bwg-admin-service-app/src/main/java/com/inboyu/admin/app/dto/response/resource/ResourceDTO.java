package com.inboyu.admin.app.dto.response.resource;

import com.inboyu.admin.domain.resource.model.Resource;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 资源DTO
 * @Author: linyu
 */
@Data
public class ResourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long resourceId;

    private String name;

    private String type;

    private String url;

    private Long size;

    public ResourceDTO(Long resourceId, String name, String type, String url, Long size) {
        this.resourceId = resourceId;
        this.name = name;
        this.type = type;
        this.url = url;
        this.size = size;
    }

    public static ResourceDTO of(Resource resource) {
        return new ResourceDTO(resource.getId().id(), resource.getName(), resource.getType().code(), resource.getUrl(), resource.getSize());
    }
}
