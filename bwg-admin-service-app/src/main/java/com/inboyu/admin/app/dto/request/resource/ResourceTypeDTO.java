package com.inboyu.admin.app.dto.request.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ResourceTypeDTO {

    /**
     * 资源类型名称
     */
    @Schema(description = "资源类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "资源类型名称不能为空")
    private String name;

    /**
     * 资源类型编码
     */
    @Schema(description = "资源类型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "资源类型编码不能为空")
    private String code;
}
