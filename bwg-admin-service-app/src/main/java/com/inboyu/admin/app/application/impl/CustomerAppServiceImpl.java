package com.inboyu.admin.app.application.impl;

import com.inboyu.admin.app.application.CustomerAppService;
import com.inboyu.admin.app.dto.request.customer.CustomerRequestDTO;
import com.inboyu.admin.app.dto.response.customer.CustomerResponseDTO;
import com.inboyu.admin.domain.customer.model.CertificateType;
import com.inboyu.admin.domain.customer.model.Customer;
import com.inboyu.admin.domain.customer.model.CustomerReal;
import com.inboyu.admin.domain.customer.service.CustomerDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CustomerAppServiceImpl implements CustomerAppService {

    @Autowired
    private CustomerDomainService customerDomainService;

    @Override
    public CustomerResponseDTO addCustomer(Long tenantId, CustomerRequestDTO customerRequestDTO) {
        Customer customer = Customer.of(null, customerRequestDTO.getName(), customerRequestDTO.getMobile());
        CustomerReal customerReal = CustomerReal.of(customer.getCustomerId(), CertificateType.of(customerRequestDTO.getCertificateType().getCode(), customerRequestDTO.getCertificateType().getTitle()), customerRequestDTO.getCertificateNumber(), customerRequestDTO.getIdCardFront(), customerRequestDTO.getIdCardBack(), customerRequestDTO.getMailAddress(), customerRequestDTO.getMail(), customerRequestDTO.getEmergencyContact(), customerRequestDTO.getEmergencyContactMobile());
        Customer newCustomer = customerDomainService.save(customer);
        CustomerReal newCustomerReal = customerDomainService.saveReal(customerReal);
        return CustomerResponseDTO.of(newCustomer,newCustomerReal);
    }

    @Override
    public List<CustomerResponseDTO> getCustomerList(Long tenantId, String keywords) {
        List<Customer> customerList = customerDomainService.list(tenantId, keywords);
        return CustomerResponseDTO.toCustomerResponseDTOList(customerList);
    }

    @Override
    public CustomerResponseDTO getCustomerByPhone(String phone) {
        Customer customer = customerDomainService.getCustomerByPhone(phone);
        log.info("查询微信租客，phone = {}, customer结果={}", phone, customer);
        if (Objects.isNull(customer)) {
            return null;
        }
        return CustomerResponseDTO.of(customer);
    }

    @Override
    public CustomerResponseDTO getByCustomerId(String customerId) {
        Customer customer = customerDomainService.getByCustomerId(customerId);
        log.info("查询微信租客，customerId = {}, customer结果={}", customerId, customer);
        if (Objects.isNull(customer)) {
            return null;
        }
        return CustomerResponseDTO.of(customer);
    }
}
