package com.inboyu.admin.app.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 微信用户信息
 */
@Data
public class CustomerWxSaveDTO {

    /**
     * 用户唯一标识
     */
    @Schema(description = "用户唯一标识", example = "cs1222")
    @NotBlank(message = "用户唯一标识不能为空")
    private String openId;

    /**
     * 用户微信平台唯一标识
     */
    @Schema(description = "用户微信平台唯一标识", example = "cs1222")
    @NotBlank(message = "微信平台用户唯一标识不能为空")
    private String unionId;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID", example = "cs1222")
    @NotBlank(message = "应用ID不能为空")
    private String appId;
}
