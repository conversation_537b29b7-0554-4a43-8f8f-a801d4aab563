package com.inboyu.admin.app.application.impl;

import com.inboyu.admin.app.application.RoleAppService;
import com.inboyu.admin.app.dto.common.role.RoleDTO;
import com.inboyu.admin.app.dto.converter.RoleAppConverter;
import com.inboyu.admin.app.dto.converter.RoleScopeAppConverter;
import com.inboyu.admin.app.dto.request.role.RoleCreateRequestDTO;
import com.inboyu.admin.app.dto.request.role.RoleUpdateRequestDTO;
import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.role.model.RoleStatus;
import com.inboyu.admin.domain.role.service.RoleDomainService;
import com.inboyu.admin.domain.role.service.RoleScopeDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class RoleAppServiceImpl implements RoleAppService {

    @Autowired
    private RoleDomainService roleDomainService;

    @Autowired
    private RoleScopeDomainService roleScopeDomainService;

    @Autowired
    private RoleAppConverter roleAppConverter;

    @Autowired
    private RoleScopeAppConverter roleScopeAppConverter;

    @Override
    public RoleDTO queryRolesInfo(Long id) {
        RoleId roleId = RoleId.of(id);
        Role role = roleDomainService.queryRoleInfo(roleId);
        return roleAppConverter.toRoleDTO(role);
    }

    /**
     * @param dto
     * @return {@link RoleDTO }
     * <AUTHOR> Dong
     * @date 2025/07/31 17:26:42
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "'role'", transType = "locker_createRole_")
    @Override
    public RoleDTO createRole(RoleCreateRequestDTO dto) {
        // 1.封装角色领域对象
        RoleStatus roleStatus = RoleStatus.create(dto.getStatus().getCode(), dto.getStatus().getTitle());
        RoleId roleId = roleDomainService.generateId();
        Role role = Role.create(roleId, dto.getTitle(), dto.getRemark(), roleStatus, Boolean.TRUE);
        // 2.创建角色
        Role createdRole = roleDomainService.createRole(role);
        // 3.封装角色权限领域对象
        List<RolePermission> rolePermissions = roleScopeAppConverter.toRolePermission(roleId, dto.getPermission());
        if (ObjectUtils.isNotEmpty(rolePermissions)) {
            // 4.保存角色权限
            roleScopeDomainService.saveAll(rolePermissions);
        }
        // 5.领域对象转响应DTO
        return roleAppConverter.toResponseDTO(createdRole, rolePermissions);
    }

    /**
     * 查询角色详情
     *
     * @param roleId
     * @return {@link RoleDTO }
     * <AUTHOR> Dong
     * @date 2025/07/31 16:56:09
     */
    @Override
    public RoleDTO getRole(Long roleId) {
        RoleId id = RoleId.of(roleId);
        // 1.查询角色详情
        Role role = roleDomainService.findByRoleIdAndEnabled(id, Boolean.TRUE);
        if (null == role) {
            throw new AppException(ResponseCode.ROLE_NOT_EXIST);
        }
        // 2.查询权限详情
        List<RolePermission> rolePermissions = roleScopeDomainService.findByRoleId(id);
        // 3.领域对象转响应DTO
        return roleAppConverter.toResponseDTO(role, rolePermissions);
    }

    /**
     * 编辑角色
     *
     * @param roleId
     * @param dto
     * @return {@link RoleDTO }
     * <AUTHOR> Dong
     * @date 2025/07/31 18:02:20
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#roleId", transType = "locker_updateRole_")
    @Override
    public RoleDTO updateRole(Long roleId, RoleUpdateRequestDTO dto) {
        RoleId id = RoleId.of(roleId);
        // 1.查询角色是否存在
        Role role = roleDomainService.findByRoleIdAndEnabled(id, Boolean.TRUE);
        if (null == role) {
            throw new AppException(ResponseCode.ROLE_NOT_EXIST);
        }
        // 2.封装角色领域对象
        RoleStatus roleStatus = RoleStatus.create(dto.getStatus().getCode(), dto.getStatus().getTitle());
        Role updateRole = Role.create(role.getId(), dto.getTitle(), dto.getRemark(), roleStatus, Boolean.TRUE);
        // 3.更新角色
        roleDomainService.updateRole(updateRole);
        // 4.更新角色权限，采用先删后添加的方式
        // 4.1.删除角色权限
        roleScopeDomainService.deleteByRoleId(role.getId());
        // 4.2.新增角色权限
        List<RolePermission> rolePermissions = roleScopeAppConverter.toRolePermission(role.getId(), dto.getPermission());
        roleScopeDomainService.saveAll(rolePermissions);
        return roleAppConverter.toResponseDTO(updateRole, rolePermissions);
    }

    /**
     * 列出角色
     *
     * @param status
     * @param title
     * @return {@link List }<{@link RoleDTO }>
     * <AUTHOR> Dong
     * @date 2025/07/31 20:53:27
     */
    @Override
    public List<RoleDTO> listRoles(String status, String title) {
        List<Role> roles = roleDomainService.findRolesByStatusAndTitleLike(status, title);
        if (ObjectUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }
        // 2.获取角色ID列表
        List<RoleId> roleIds = roles.stream().map(Role::getId).toList();
        // 3.批量查询角色权限信息
        Map<RoleId, List<RolePermission>> rolePermissions = roleScopeDomainService.findAngGroupByRoleIds(roleIds);
        // 4.转换为DTO列表
        return roles.stream()
                .map(role -> {
                    List<RolePermission> permissions = rolePermissions.getOrDefault(role.getId(), Collections.emptyList());
                    return roleAppConverter.toResponseDTO(role, permissions);
                }).toList();
    }
}
