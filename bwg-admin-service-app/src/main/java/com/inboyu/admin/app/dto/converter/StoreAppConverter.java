package com.inboyu.admin.app.dto.converter;

import java.math.BigDecimal;

import org.springframework.stereotype.Component;

import com.inboyu.admin.app.dto.request.store.StoreCreateRequestDTO;
import com.inboyu.admin.app.dto.request.store.StoreUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.store.StoreCreateResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreDTO;
import com.inboyu.admin.app.dto.response.store.StoreUpdateResponseDTO;
import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.GeoPoint;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.dto.common.DeptDTO;
import com.inboyu.admin.dto.common.GeoPointDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;

/**
 * <AUTHOR> Dong
 * @date 2025年08月02日 11:31
 */
@Component
public class StoreAppConverter {
    public Store toStore(StoreId storeId, StoreCreateRequestDTO dto) {
        return Store.builder()
                .id(storeId)
                .title(dto.getTitle())
                .deptId(DeptId.of(Long.valueOf(dto.getDeptId())))
                .provinceCode(dto.getProvinceCode())
                .provinceName(dto.getProvinceName())
                .cityCode(dto.getCityCode())
                .cityName(dto.getCityName())
                .countyCode(dto.getCountyCode())
                .countyName(dto.getCountyName())
                .address(dto.getAddress())
                .coordinate(GeoPoint.of(new BigDecimal(dto.getCoordinate().getLongitude()), new BigDecimal(dto.getCoordinate().getLatitude())))
                .build();
    }

    public Store toStore(StoreId storeId, StoreUpdateRequestDTO dto) {
        return Store.builder()
                .id(storeId)
                .title(dto.getTitle())
                .deptId(DeptId.of(Long.valueOf(dto.getDeptId())))
                .provinceCode(dto.getProvinceCode())
                .provinceName(dto.getProvinceName())
                .cityCode(dto.getCityCode())
                .cityName(dto.getCityName())
                .countyCode(dto.getCountyCode())
                .countyName(dto.getCountyName())
                .address(dto.getAddress())
                .coordinate(GeoPoint.of(new BigDecimal(dto.getCoordinate().getLongitude()), new BigDecimal(dto.getCoordinate().getLatitude())))
                .build();
    }

    public StoreCreateResponseDTO toCreateDTO(Dept dept, Store store) {
        return StoreCreateResponseDTO.builder()
                .storeId(store.getId().getValue().toString())
                .title(store.getTitle())
                .dept(DeptDTO.builder()
                        .deptId(dept.getId().getValue().toString())
                        .title(dept.getTitle())
                        .build())
                .provinceCode(store.getProvinceCode())
                .provinceName(store.getProvinceName())
                .cityCode(store.getCityCode())
                .cityName(store.getCityName())
                .countyCode(store.getCountyCode())
                .countyName(store.getCountyName())
                .address(store.getAddress())
                .coordinate(new GeoPointDTO(store.getCoordinate().getLongitude().toString(),
                        store.getCoordinate().getLatitude().toString()))
                .build();
    }

    public StoreUpdateResponseDTO toUpdateDTO(Dept dept, Store store) {
        return StoreUpdateResponseDTO.builder()
                .storeId(store.getId().getValue().toString())
                .title(store.getTitle())
                .dept(DeptDTO.builder()
                        .deptId(dept.getId().getValue().toString())
                        .title(dept.getTitle())
                        .build())
                .provinceCode(store.getProvinceCode())
                .provinceName(store.getProvinceName())
                .cityCode(store.getCityCode())
                .cityName(store.getCityName())
                .countyCode(store.getCountyCode())
                .countyName(store.getCountyName())
                .address(store.getAddress())
                .coordinate(new GeoPointDTO(store.getCoordinate().getLongitude().toString(),
                        store.getCoordinate().getLatitude().toString()))
                .build();
    }

    public StoreDetailResponseDTO toDetailDTO(Dept dept, Store store) {
        return StoreDetailResponseDTO.builder()
                .storeId(store.getId().getValue().toString())
                .title(store.getTitle())
                .dept(DeptDTO.builder()
                        .deptId(dept.getId().getValue().toString())
                        .title(dept.getTitle())
                        .build())
                .provinceCode(store.getProvinceCode())
                .provinceName(store.getProvinceName())
                .cityCode(store.getCityCode())
                .cityName(store.getCityName())
                .countyCode(store.getCountyCode())
                .countyName(store.getCountyName())
                .address(store.getAddress())
                .coordinate(new GeoPointDTO(store.getCoordinate().getLongitude().toString(),
                        store.getCoordinate().getLatitude().toString()))
                .build();
    }

    public StoreDTO toDTO(Store store) {
        return StoreDTO.builder()
                .storeId(store.getId().getValue().toString())
                .title(store.getTitle())
                .provinceCode(store.getProvinceCode())
                .provinceName(store.getProvinceName())
                .cityCode(store.getCityCode())
                .cityName(store.getCityName())
                .countyCode(store.getCountyCode())
                .countyName(store.getCountyName())
                .address(store.getAddress())
                .build();
    }
}
