package com.inboyu.admin.app.dto.request.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BusinessTypeDTO {

    /**
     * 业务标识名称
     */
    @Schema(description = "业务标识编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "业务标识编码不能为空")
    private String name;

    /**
     * 业务标识编码
     */
    @Schema(description = "业务标识编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "业务标识编码不能为空")
    private String code;
}
