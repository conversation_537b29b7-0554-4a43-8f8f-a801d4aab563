package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.response.dept.DeptCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptListResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptUpdateResponseDTO;
import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年07月29日 15:46
 */
@Component
public class DeptAppConverter {

    public DeptCreateResponseDTO toDeptAddResponseDTO(Dept dept, Dept parentDept) {
        DeptCreateResponseDTO dto = new DeptCreateResponseDTO();
        dto.setTitle(dept.getTitle());
        dto.setDeptId(dept.getId().getValue().toString());
        DeptCreateResponseDTO.ParentDeptResponseDTO parentDTO = new DeptCreateResponseDTO.ParentDeptResponseDTO();
        parentDTO.setDeptId(parentDept.getId().getValue().toString());
        parentDTO.setTitle(parentDept.getTitle());
        dto.setDept(parentDTO);
        return dto;
    }

    /**
     * 转换为更新组织响应DTO
     *
     * @param dept 更新后的组织
     * @param parentDept 父组织
     * @return {@link DeptUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:45:00
     */
    public DeptUpdateResponseDTO toDeptUpdateResponseDTO(Dept dept, Dept parentDept) {
        DeptUpdateResponseDTO dto = new DeptUpdateResponseDTO();
        dto.setTitle(dept.getTitle());
        dto.setDeptId(dept.getId().getValue().toString());
        // 设置父组织信息
        DeptUpdateResponseDTO.ParentDeptResponseDTO parentDTO = new DeptUpdateResponseDTO.ParentDeptResponseDTO();
        parentDTO.setDeptId(parentDept.getId().getValue().toString());
        parentDTO.setTitle(parentDept.getTitle());
        dto.setDept(parentDTO);
        return dto;
    }

    /**
     * 构建组织树形结构
     *
     * @param deptList 所有组织列表
     * @return 根组织
     * <AUTHOR> Dong
     * @date 2025/07/30 14:38:50
     */
    public DeptListResponseDTO buildDeptTree(List<Dept> deptList) {
        // 找到根组织（deptId = 1）
        Dept rootDept = deptList.stream()
                .filter(dept -> dept.getParentDeptId().getValue() == 0L)
                .findFirst()
                .orElse(null);
        if (rootDept == null) {
            return null;
        }
        // 构建根组织响应DTO
        DeptListResponseDTO rootResponse = new DeptListResponseDTO();
        rootResponse.setDeptId(rootDept.getId().getValue().toString());
        rootResponse.setTitle(rootDept.getTitle());
        // 递归构建子组织
        rootResponse.setChildren(buildChildren(rootDept.getId(), deptList));
        return rootResponse;
    }

    /**
     * 递归构建子组织
     *
     * @param parentId 父组织ID
     * @param deptList 所有组织列表
     * @return 子组织列表
     */
    private List<DeptListResponseDTO> buildChildren(DeptId parentId, List<Dept> deptList) {
        return deptList.stream()
                .filter(dept -> parentId.equals(dept.getParentDeptId()))
                .map(dept -> {
                    DeptListResponseDTO child = new DeptListResponseDTO();
                    child.setDeptId(dept.getId().getValue().toString());
                    child.setTitle(dept.getTitle());
                    child.setChildren(buildChildren(dept.getId(), deptList));
                    return child;
                }).toList();
    }
}
