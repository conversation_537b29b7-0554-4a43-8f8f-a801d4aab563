package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.CustomerAppService;
import com.inboyu.admin.app.dto.request.customer.CustomerRequestDTO;
import com.inboyu.admin.app.dto.response.customer.CustomerResponseDTO;
import com.inboyu.spring.cloud.starter.context.base.utils.SystemContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/customers")
@Tag(name = "租赁人信息管理")
public class CustomerController {

    @Autowired
    private CustomerAppService customerAppService;

    @PostMapping
    @Operation(summary = "新增租赁人信息")
    public CustomerResponseDTO addCustomer(@RequestBody @Validated CustomerRequestDTO customerRequestDTO) {
        Long tenantId = Long.valueOf(SystemContextUtils.getTenantId());
        return customerAppService.addCustomer(tenantId, customerRequestDTO);
    }

    @GetMapping("/list")
    @Operation(summary = "查询租赁人信息列表")
    public List<CustomerResponseDTO> getCustomerList(@RequestParam(value = "keywords", defaultValue = "", required = false) String keywords) {
        Long tenantId = Long.valueOf(SystemContextUtils.getTenantId());
        return customerAppService.getCustomerList(tenantId, keywords);
    }

    @GetMapping("")
    @Operation(summary = "根据手机号查询租客信息")
    public CustomerResponseDTO getCustomerByPhone(@RequestParam(value = "phone") String phone) {

        return customerAppService.getCustomerByPhone(phone);
    }

    @GetMapping("/{customerId}")
    @Operation(summary = "根据customerId查询租客信息")
    public CustomerResponseDTO getByCustomerId(@PathVariable(value = "customerId") String customerId) {

        return customerAppService.getByCustomerId(customerId);
    }
}

