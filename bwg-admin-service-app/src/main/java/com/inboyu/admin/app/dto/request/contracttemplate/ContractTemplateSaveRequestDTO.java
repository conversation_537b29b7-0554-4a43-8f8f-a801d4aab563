package com.inboyu.admin.app.dto.request.contracttemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 新增/编辑合同模板请求实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractTemplateSaveRequestDTO {

    @NotBlank(message = "门店Id不能为空")
    @Schema(title = "门店Id",requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeId;

    @Schema(title = "在线合同条款")
    private String onlineContract;

    @NotBlank(message = "电子签章编号不能为空")
    @Schema(title = "电子签章编号",requiredMode = Schema.RequiredMode.REQUIRED)
    private String esignNumber;

    @NotBlank(message = "缴费周期不能为空")
    @Schema(title = "缴费周期",requiredMode = Schema.RequiredMode.REQUIRED)
    private String paymentCycle;

    @NotBlank(message = "推送方式不能为空")
    @Schema(title = "推送方式",requiredMode = Schema.RequiredMode.REQUIRED)
    private String pushType;

    @NotNull(message = "起租日期可选范围不能为空")
    @Schema(title = "起租日期可选范围",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rentDateRange;

    @NotNull(message = "滞纳金比例不能为空")
    @Schema(title = "滞纳金比例",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal overdueFineProportion;
}
