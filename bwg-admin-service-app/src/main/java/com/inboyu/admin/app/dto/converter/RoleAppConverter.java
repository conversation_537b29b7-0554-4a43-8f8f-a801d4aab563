package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.common.role.RoleDTO;
import com.inboyu.admin.app.dto.common.role.RolePermissionDTO;
import com.inboyu.admin.app.dto.common.role.RoleStatusDTO;
import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.role.model.RoleStatus;
import com.inboyu.util.convert.PojoConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RoleAppConverter {

    public RoleDTO toRoleDTO(Role role) {
        return PojoConvertUtil.convert(role, RoleDTO.class);
    }

    public RoleDTO toResponseDTO(Role role, List<RolePermission> rolePermissions) {
        RoleDTO dto = new RoleDTO();
        dto.setRoleId(role.getId() != null ? role.getId().getValue().toString() : null);
        dto.setTitle(role.getTitle());
        dto.setRemark(role.getRemark());
        // 封装权限转换逻辑
        Map<String, List<RolePermissionDTO>> permissionMap = convertRolePermissions(rolePermissions);
        dto.setPermission(permissionMap);
        // 状态转换
        RoleStatus status = role.getStatus();
        if (status != null) {
            RoleStatusDTO statusDTO = new RoleStatusDTO();
            statusDTO.setCode(status.getCode());
            statusDTO.setTitle(status.getValue());
            dto.setStatus(statusDTO);
        }
        return dto;
    }

    /**
     * 转换角色权限列表为DTO格式
     *
     * @param rolePermissions 角色权限列表
     * @return 按groupCode分组的权限DTO映射
     */
    private Map<String, List<RolePermissionDTO>> convertRolePermissions(List<RolePermission> rolePermissions) {
        Map<String, List<RolePermissionDTO>> permissionMap = new HashMap<>();
        if (rolePermissions == null || rolePermissions.isEmpty()) {
            return permissionMap;
        }
        // 按groupCode分组
        Map<String, List<RolePermission>> groupedPermissions = rolePermissions.stream()
                .collect(Collectors.groupingBy(RolePermission::getGroupCode));
        // 转换为DTO格式
        for (Map.Entry<String, List<RolePermission>> entry : groupedPermissions.entrySet()) {
            String groupCode = entry.getKey();
            List<RolePermissionDTO> permissionDTOs = entry.getValue().stream()
                    .filter(rolePermission -> rolePermission.getPermission() != null &&
                            StringUtils.isNotBlank(rolePermission.getPermission().getValue()))
                    .map(this::convertToRolePermissionDTO)
                    .toList();
            // 只有当permissionDTOs不为空时才加入map
            if (permissionDTOs.isEmpty()) {
                // 这里目的是方便前端处理，只有菜单没有权限的情况下返回空数组
                permissionMap.put(groupCode, List.of());
            } else {
                permissionMap.put(groupCode, permissionDTOs);

            }
        }
        return permissionMap;
    }

    /**
     * 转换单个角色权限为DTO
     *
     * @param rolePermission 角色权限
     * @return 角色权限DTO
     */
    private RolePermissionDTO convertToRolePermissionDTO(RolePermission rolePermission) {
        RolePermissionDTO dto = new RolePermissionDTO();
        dto.setPermission(rolePermission.getPermission().getValue());
        dto.setHidden(Boolean.TRUE.equals(rolePermission.getHidden()));
        dto.setDisabled(Boolean.TRUE.equals(rolePermission.getDisabled()));
        return dto;
    }
}
