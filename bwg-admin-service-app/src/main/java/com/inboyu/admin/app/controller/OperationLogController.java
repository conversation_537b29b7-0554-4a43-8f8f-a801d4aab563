package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.OperationLogAppService;
import com.inboyu.admin.app.dto.request.operationlog.OperationLogCollectRequestDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogCollectResponseDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogPageResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name="操作日志")
@RestController
@RequestMapping("/api/v1/operation-log")
public class OperationLogController {
    @Autowired
    private OperationLogAppService operationLogAppService;

    @PostMapping
    @Operation(summary = "操作日志采集")
    public OperationLogCollectResponseDTO collect(@Parameter(description = "操作日志采集请求实体",required = true) @RequestBody OperationLogCollectRequestDTO dto) {
        return operationLogAppService.collect(dto);
    }

    @GetMapping("/page")
    @Operation(summary = "操作日志查询")
    public Pagination<OperationLogPageResponseDTO> page(@Parameter(description = "操作人") @RequestParam(required = false) String operator,
                                                        @Parameter(description = "操作类型") @RequestParam(required = false) String operationType,
                                                        @Parameter(description = "是否成功") @RequestParam(required = false) Boolean success,
                                                        @Parameter(description = "操作开始时间") @RequestParam(required = false) String operationTimeStart,
                                                        @Parameter(description = "操作结束时间") @RequestParam(required = false) String operationTimeEnd,
                                                        @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer pageNum,
                                                        @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        return operationLogAppService.page(operator, operationType, success, operationTimeStart, operationTimeEnd, pageNum, pageSize);
    }
}
