package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.common.role.RolePermissionDTO;
import com.inboyu.admin.domain.role.model.Permission;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR> Dong
 * @date 2025年07月31日 15:50
 */
@Component
public class RoleScopeAppConverter {
    public List<RolePermission> toRolePermission(RoleId roleId, Map<String, List<RolePermissionDTO>> map) {
        if (ObjectUtils.isEmpty(map)) {
            return Collections.emptyList();
        }
        return map.entrySet().stream()
                .flatMap(entry -> {
                    String groupCode = entry.getKey();
                    List<RolePermissionDTO> permissions = entry.getValue();
                    // 如果权限列表为空，创建一个默认的 RolePermission
                    if (ObjectUtils.isEmpty(permissions)) {
                        return Stream.of(RolePermission.create(
                                roleId,
                                groupCode,
                                Permission.create(""), // 空权限
                                false, // 默认不隐藏
                                false  // 默认不禁用
                        ));
                    }
                    // 如果权限列表不为空，正常处理
                    return permissions.stream()
                            .map(dto -> RolePermission.create(
                                    roleId,
                                    groupCode,
                                    Permission.create(dto.getPermission()),
                                    dto.isHidden(),
                                    dto.isDisabled()
                            ));
                })
                .toList();
    }
}
