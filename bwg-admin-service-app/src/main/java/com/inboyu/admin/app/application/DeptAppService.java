package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.request.dept.DeptCreateRequestDTO;
import com.inboyu.admin.app.dto.response.dept.DeptCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptListResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptUpdateResponseDTO;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:58
 */
public interface DeptAppService {
    /**
     * 添加组织
     *
     * @param dto
     * @return {@link DeptCreateResponseDTO }
     * <AUTHOR>
     * @date 2025/07/30 09:16:18
     */
    DeptCreateResponseDTO createDept(DeptCreateRequestDTO dto);

    /**
     * 编辑组织
     *
     * @param deptId
     * @param title
     * @return {@link DeptUpdateResponseDTO }
     * <AUTHOR>
     * @date 2025/07/31 09:40:00
     */
    DeptUpdateResponseDTO updateDept(Long deptId, String title);

    /**
     * 列出组织
     *
     * @return {@link DeptListResponseDTO }
     * <AUTHOR>
     * @date 2025/07/30 14:08:23
     */
    DeptListResponseDTO listDept();

    /**
     * 删除组织
     *
     * @param deptId
     * <AUTHOR> Dong
     * @date 2025/08/04 15:47:11
     */
    void deleteDept(Long deptId);
}
