package com.inboyu.admin.app.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.inboyu.admin.app.application.StoreAppService;
import com.inboyu.admin.app.dto.request.store.StoreCreateRequestDTO;
import com.inboyu.admin.app.dto.request.store.StoreUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.store.StoreCreateResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreDTO;
import com.inboyu.admin.app.dto.response.store.StoreUpdateResponseDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Dong
 * @date 2025年07月30日 15:27
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/stores")
@Tag(name = "门店控制器")
public class StoreController {

    @Autowired
    private StoreAppService storeAppService;

    @PostMapping
    @Operation(summary = "添加门店")
    public StoreCreateResponseDTO createStore(@Valid @RequestBody StoreCreateRequestDTO dto) {
        return storeAppService.createStore(dto);
    }

    @GetMapping("/{storeId}")
    @Operation(summary = "查询门店详情")
    public StoreDetailResponseDTO getStore(@Schema(description = "门店id") @PathVariable String storeId) {
        return storeAppService.getStore(Long.valueOf(storeId));
    }

    @PostMapping("/{storeId}")
    @Operation(summary = "更新门店")
    public StoreUpdateResponseDTO updateStore(@Schema(description = "门店id") @PathVariable String storeId,
                                              @Valid @RequestBody StoreUpdateRequestDTO dto) {
        return storeAppService.updateStore(Long.valueOf(storeId), dto);
    }

    @GetMapping
    @Operation(summary = "列出门店")
    public Pagination<StoreDTO> pageStores(@RequestParam @Schema(description = "当前页") Integer pageNum,
                                           @RequestParam @Schema(description = "每页条数") Integer pageSize,
                                           @RequestParam(required = false) @Schema(description = "门店名称(支持模糊)") String keywords,
                                           @RequestParam(required = false) @Schema(description = "组织id") String deptId) {
        Long dept = null;
        if (StringUtils.isNotBlank(deptId)) {
            dept = Long.valueOf(deptId);
        }
        return storeAppService.pageStores(pageNum, pageSize, keywords, dept);
    }

    @GetMapping("/filter")
    @Operation(summary = "条件检索")
    public List<StoreDTO> filter(@RequestHeader("x-role-scope") String roleScope) {
        String[] storeIds = roleScope.split(",");
        return storeAppService.filterByStoreIds(storeIds);
    }
}
