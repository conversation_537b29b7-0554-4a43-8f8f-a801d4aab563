package com.inboyu.admin.app.dto.request.staff;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

import com.inboyu.admin.dto.request.StaffDeptRequestDTO;
import com.inboyu.admin.dto.response.StaffStatusDTO;

/**
 * 添加员工请求参数
 *
 * <AUTHOR> Dong
 * @date 2025年08月05日 10:17
 */
@Data
public class StaffRequestDTO {
    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    private String phone;

    @Schema(description = "员工状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态必选")
    private StaffStatusDTO status;

    @Schema(description = "组织信息")
    private List<StaffDeptRequestDTO> depts;
}
