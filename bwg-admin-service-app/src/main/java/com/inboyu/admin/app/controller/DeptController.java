package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.DeptAppService;
import com.inboyu.admin.app.dto.request.dept.DeptCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dept.DeptUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dept.DeptCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptListResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptUpdateResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:34
 */
@RestController
@RequestMapping("api/v1/depts")
@Tag(name = "组织控制器")
public class DeptController {
    @Autowired
    private DeptAppService deptAppService;

    @PostMapping
    @Operation(summary = "添加组织")
    public DeptCreateResponseDTO createDept(@RequestBody @Valid DeptCreateRequestDTO request) {
        return deptAppService.createDept(request);
    }

    @PostMapping("{deptId}")
    @Operation(summary = "编辑组织")
    public DeptUpdateResponseDTO updateDept(@PathVariable String deptId, @RequestBody @Valid DeptUpdateRequestDTO request) {
        return deptAppService.updateDept(Long.valueOf(deptId), request.getTitle());
    }

    @GetMapping
    @Operation(summary = "列出组织")
    public DeptListResponseDTO listDept() {
        return deptAppService.listDept();
    }

    @DeleteMapping("{deptId}")
    @Operation(summary = "删除组织")
    public void deleteDept(@Schema(description = "组织id") @PathVariable String deptId) {
        deptAppService.deleteDept(Long.valueOf(deptId));
    }
}
