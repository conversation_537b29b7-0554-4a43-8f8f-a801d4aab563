package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.request.contracttemplate.ContractTemplateSaveRequestDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateGetResponseDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateSaveResponseDTO;

/**
 * 合同模板应用服务
 * <AUTHOR>
 */
public interface ContractTemplateAppService {
    /**
     * 获取合同模板
     *
     * @param storeId
     * @return {@link ContractTemplateGetResponseDTO}
     */
    ContractTemplateGetResponseDTO get(String storeId);

    /**
     * 新增/编辑合同模板
     *
     * @param dto
     * @return {@link ContractTemplateSaveResponseDTO}
     */
    ContractTemplateSaveResponseDTO save(ContractTemplateSaveRequestDTO dto);
}
