package com.inboyu.admin.app.dto.response.customer;

import com.inboyu.admin.domain.customer.model.CustomerWx;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 微信用户信息返回
 */
@Data
public class CustomerWxResponseDTO {
    private String openId;
    private Long customerId;
    private String unionId;
    private String appId;
    private Long tenantId;
    private String authTime;

    public static CustomerWxResponseDTO of(CustomerWx customerWx) {
        LocalDateTime authTime = customerWx.getAuthTime();
        CustomerWxResponseDTO customerWxResponseDTO = new CustomerWxResponseDTO();
        customerWxResponseDTO.setOpenId(customerWx.getOpenId());
        customerWxResponseDTO.setCustomerId(Objects.isNull(customerWx.getCustomerId()) ? null : customerWx.getCustomerId().getValue());
        customerWxResponseDTO.setUnionId(customerWx.getUnionId());
        if (Objects.nonNull(authTime)) {
            customerWxResponseDTO.setAuthTime(authTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        customerWxResponseDTO.setAppId(customerWx.getAppId());
        return customerWxResponseDTO;
    }
}
