package com.inboyu.admin.app.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerRequestDTO implements Serializable {

    /**
     * 租赁人姓名
     */
    @Schema(description = "租赁人姓名", example = "王五")
    @NotBlank(message = "租赁人姓名不能为空")
    private String name;

    /**
     * 租赁人手机号
     */
    @Schema(description = "租赁人手机号", example = "13889890909")
    @NotBlank(message = "租赁人手机号不能为空")
    private String mobile;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型")
    private CertificateTypeDTO certificateType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码", example = "350824199809231111")
    @NotBlank(message = "证件号码不能为空")
    private String certificateNumber;

    /**
     * 身份证正面
     */
    @Schema(description = "身份证正面")
//    @NotBlank(message = "身份证正面不能为空")
    private String idCardFront;

    /**
     * 身份证反面
     */
    @Schema(description = "身份证反面")
//    @NotBlank(message = "身份证反面不能为空")
    private String idCardBack;

    /**
     * 通信地址
     */
    @Schema(description = "通信地址", example = "厦门市湖里区莲前街道前埔11号")
//    @NotBlank(message = "通信地址不能为空")
    private String mailAddress;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String mail;

    /**
     * 紧急联系人
     */
    @Schema(description = "紧急联系人", example = "张三")
//    @NotBlank(message = "紧急联系人不能为空")
    private String emergencyContact;

    /**
     * 紧急联系人手机
     */
    @Schema(description = "紧急联系人手机", example = "13999999999")
//    @NotBlank(message = "紧急联系人手机不能为空")
    private String emergencyContactMobile;

}
