package com.inboyu.admin.app.application;

import java.util.List;

import com.inboyu.admin.app.dto.request.store.StoreCreateRequestDTO;
import com.inboyu.admin.app.dto.request.store.StoreUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.store.StoreCreateResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreDTO;
import com.inboyu.admin.app.dto.response.store.StoreUpdateResponseDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

/**
 * <AUTHOR>
 * @date 2025年08月02日 11:22
 */
public interface StoreAppService {
    /**
     * 添加门店
     *
     * @param dto
     * @return {@link StoreCreateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 11:23:56
     */
    StoreCreateResponseDTO createStore(StoreCreateRequestDTO dto);

    /**
     * 查询门店详情
     *
     * @param storeId
     * @return {@link StoreDetailResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    StoreDetailResponseDTO getStore(Long storeId);

    /**
     * 更新门店
     *
     * @param storeId
     * @param dto
     * @return {@link StoreUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    StoreUpdateResponseDTO updateStore(Long storeId, StoreUpdateRequestDTO dto);

    /**
     * 列出门店
     *
     * @param pageNum
     * @param pageSize
     * @param keywords
     * @param deptId
     * @return {@link Pagination }<{@link StoreDTO }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:38:24
     */
    Pagination<StoreDTO> pageStores(Integer pageNum, Integer pageSize, String keywords, Long deptId);

    /**
     * 根据门店ID获取门店信息
     * @param storeIds
     * @return
     */
    List<StoreDTO> filterByStoreIds(String[] storeIds);
}
