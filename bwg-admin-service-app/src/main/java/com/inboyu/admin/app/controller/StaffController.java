package com.inboyu.admin.app.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.inboyu.admin.api.StaffFeignClient;
import com.inboyu.admin.app.application.StaffAppService;
import com.inboyu.admin.app.dto.request.staff.StaffRequestDTO;
import com.inboyu.admin.dto.response.StaffDTO;
import com.inboyu.admin.dto.response.StaffDetailResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuPermissionsResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR> Dong
 * @date 2025年08月01日 16:13
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/staffs")
@Tag(name = "员工控制器")
public class StaffController implements StaffFeignClient {
    @Autowired
    private StaffAppService staffAppService;

    @Override
    @GetMapping("{staffId}/permissions")
    @Operation(summary = "获取员工菜单权限")
    public StaffMenuResponseDTO getStaffPermissions(
            @Schema(description = "员工id") @PathVariable String staffId) {
        List<StaffMenuPermissionsResponseDTO> menus = staffAppService.getStaffPermissions(Long.valueOf(staffId));
        StaffMenuResponseDTO dto = new StaffMenuResponseDTO();
        dto.setMenus(menus);
        return dto;
    }

    @GetMapping("/permissions")
    @Operation(summary = "获取管理员菜单权限")
    @Override
    public StaffMenuResponseDTO getAdminPermissions() {
        List<StaffMenuPermissionsResponseDTO> menus = staffAppService.getAllPermission();
        StaffMenuResponseDTO dto = new StaffMenuResponseDTO();
        dto.setMenus(menus);
        return dto;
    }

    @PostMapping
    @Operation(summary = "添加员工")
    public StaffDTO createStaff(@RequestBody @Valid StaffRequestDTO dto) {
        return staffAppService.createStaff(dto);
    }

    @GetMapping("/{staffId}")
    @Operation(summary = "员工详情")
    public StaffDetailResponseDTO getStaffDetail(@PathVariable String staffId) {
        return staffAppService.getStaffDetail(Long.valueOf(staffId));
    }

    @GetMapping
    @Operation(summary = "列出员工")
    public Pagination<StaffDetailResponseDTO> pageByKeywordLike(
            @RequestParam @Schema(description = "当前页") Integer pageNum,
            @RequestParam @Schema(description = "每页条数") Integer pageSize,
            @RequestParam(required = false) @Schema(description = "关键词") String keywords,
            @RequestParam(required = false) @Schema(description = "角色id") String role,
            @RequestParam(required = false) @Schema(description = "状态") String status,
            @RequestParam(required = false) @Schema(description = "部门id") String deptId) {
        return staffAppService.pageByKeywordLike(pageNum, pageSize, keywords, status, role, deptId);
    }

    @PostMapping("{staffId}")
    @Operation(summary = "编辑员工")
    public StaffDTO updateStaff(@Schema(description = "员工ID") @PathVariable String staffId,
            @RequestBody @Valid StaffRequestDTO dto) {
        return staffAppService.updateStaff(Long.valueOf(staffId), dto);
    }

}
