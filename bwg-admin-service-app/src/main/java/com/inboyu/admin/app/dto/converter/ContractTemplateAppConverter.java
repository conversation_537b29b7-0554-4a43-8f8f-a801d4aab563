package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.request.contracttemplate.ContractTemplateSaveRequestDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateGetResponseDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateSaveResponseDTO;
import com.inboyu.admin.domain.contracttemplate.model.ContractTemplate;
import com.inboyu.admin.domain.contracttemplate.model.PaymentCycle;
import com.inboyu.admin.domain.contracttemplate.model.PushType;
import com.inboyu.admin.domain.contracttemplate.model.StoreId;
import org.springframework.stereotype.Component;

/**
 * 合同模板请求与领域对象互转
 * <AUTHOR>
 */
@Component
public class ContractTemplateAppConverter {

    /**
     * 新增/编辑合同模板请求对象转换为合同模板领域对象
     *
     * @param dto
     * @return {@link ContractTemplate }
     */
    public ContractTemplate toContractTemplate(ContractTemplateSaveRequestDTO dto) {
        return ContractTemplate.of(StoreId.of(dto.getStoreId()), dto.getOnlineContract(), dto.getEsignNumber(),
                PaymentCycle.of(dto.getPaymentCycle(),null), PushType.of(dto.getPushType(),null),
                dto.getRentDateRange(), dto.getOverdueFineProportion());
    }

    /**
     * 合同模板领域对象转换为合同模板获取响应对象
     *
     * @param contractTemplate
     * @return {@link ContractTemplateGetResponseDTO }
     */
    public ContractTemplateGetResponseDTO toContractTemplateGetResponseDTO(ContractTemplate contractTemplate) {
        ContractTemplateGetResponseDTO dto = new ContractTemplateGetResponseDTO();
        dto.setStoreId(String.valueOf(contractTemplate.getStoreId().getValue()));
        dto.setOnlineContract(contractTemplate.getOnlineContract());
        dto.setEsignNumber(contractTemplate.getEsignNumber());
        dto.setPaymentCycle(contractTemplate.getPaymentCycle().getCode());
        dto.setPushType(contractTemplate.getPushType().getCode());
        dto.setRentDateRange(contractTemplate.getRentDateRange());
        dto.setOverdueFineProportion(contractTemplate.getOverdueFineProportion());
        return dto;
    }

    /**
     * 合同模板领域对象转换为新增/编辑合同模板响应对象
     *
     * @param contractTemplate
     * @return {@link ContractTemplateSaveResponseDTO }
     */
    public ContractTemplateSaveResponseDTO toContractTemplateSaveResponseDTO(ContractTemplate contractTemplate) {
        ContractTemplateSaveResponseDTO dto = new ContractTemplateSaveResponseDTO();
        dto.setStoreId(String.valueOf(contractTemplate.getStoreId().getValue()));
        dto.setOnlineContract(contractTemplate.getOnlineContract());
        dto.setEsignNumber(contractTemplate.getEsignNumber());
        dto.setPaymentCycle(contractTemplate.getPaymentCycle().getCode());
        dto.setPushType(contractTemplate.getPushType().getCode());
        dto.setRentDateRange(contractTemplate.getRentDateRange());
        dto.setOverdueFineProportion(contractTemplate.getOverdueFineProportion());
        return dto;
    }
}
