package com.inboyu.admin.app.dto.response.contracttemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 新增/编辑合同模板响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractTemplateSaveResponseDTO {

    @Schema(title = "门店ID")
    private String storeId;

    @Schema(title = "在线合同条款")
    private String onlineContract;

    @Schema(title = "电子签章编号")
    private String esignNumber;

    @Schema(title = "缴费周期")
    private String paymentCycle;

    @Schema(title = "推送方式")
    private String pushType;

    @Schema(title = "起租日期可选范围")
    private Integer rentDateRange;

    @Schema(title = "滞纳金比例")
    private BigDecimal overdueFineProportion;
}
