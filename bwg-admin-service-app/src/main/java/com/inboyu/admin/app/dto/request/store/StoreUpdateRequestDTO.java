package com.inboyu.admin.app.dto.request.store;

import com.inboyu.admin.dto.common.GeoPointDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 门店更新请求实体
 *
 * <AUTHOR>
 * @date 2025年08月02日 16:00
 */
@Data
public class StoreUpdateRequestDTO {

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "门店名称不能为空")
    private String title;

    @Schema(description = "部门id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "部门id不能为空")
    private String deptId;

    @Schema(description = "省编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    @Schema(description = "省名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "省名称不能为空")
    private String provinceName;

    @Schema(description = "市编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "市编码不能为空")
    private String cityCode;

    @Schema(description = "市名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "市名称不能为空")
    private String cityName;

    @Schema(description = "县（区）编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "县（区）编码不能为空")
    private String countyCode;

    @Schema(description = "县（区）名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "县（区）名称不能为空")
    private String countyName;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "详细地址不能为空")
    private String address;

    @Schema(description = "坐标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "经纬度不能为空")
    private GeoPointDTO coordinate;
}
