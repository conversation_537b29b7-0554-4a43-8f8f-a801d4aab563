package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.request.dict.DictCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dict.DictUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dict.DictCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictListResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictUpdateResponseDTO;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 14:58
 */
public interface DictAppService {

    /**
     * 添加字典
     *
     * @param type
     * @param dto
     * @return {@link DictCreateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/04 11:16:18
     */
    DictCreateResponseDTO createDict(String type, DictCreateRequestDTO dto);

    /**
     * 删除字典
     *
     * @param type
     * @param code
     * @return void
     * <AUTHOR> Dong
     * @date 2025/08/04 11:16:18
     */
    void deleteDict(String type, String code);

    /**
     * 更新字典
     *
     * @param type
     * @param code
     * @param dto
     * @return {@link DictUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/04 11:16:18
     */
    DictUpdateResponseDTO updateDict(String type, String code, DictUpdateRequestDTO dto);

    /**
     * 查询字典列表
     *
     * @param type
     * @return {@link DictListResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/04 11:16:18
     */
    DictListResponseDTO listDict(String type);

}
