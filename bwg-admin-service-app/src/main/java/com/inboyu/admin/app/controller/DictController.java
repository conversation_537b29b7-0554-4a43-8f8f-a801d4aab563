package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.DictAppService;
import com.inboyu.admin.app.dto.request.dict.DictCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dict.DictUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dict.DictCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictListResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictUpdateResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 15:58
 */
@RestController
@RequestMapping("api/v1/dict")
@Tag(name = "字典控制器")
public class DictController {

    @Autowired
    private DictAppService dictAppService;

    @PostMapping("{type}")
    @Operation(summary = "创建字典")
    public DictCreateResponseDTO createDict(@PathVariable String type, @RequestBody @Valid DictCreateRequestDTO request) {
        return dictAppService.createDict(type, request);
    }

    @DeleteMapping("{type}/{code}")
    @Operation(summary = "删除字典")
    public void deleteDict(@PathVariable String type, @PathVariable String code) {
        dictAppService.deleteDict(type, code);
    }

    @PostMapping("{type}/{code}")
    @Operation(summary = "更新字典")
    public DictUpdateResponseDTO updateDict(@PathVariable String type, @PathVariable String code, @RequestBody @Valid DictUpdateRequestDTO request) {
        return dictAppService.updateDict(type, code, request);
    }

    @GetMapping("{type}")
    @Operation(summary = "查询字典")
    public DictListResponseDTO listDict(@PathVariable String type) {
        return dictAppService.listDict(type);
    }
}
