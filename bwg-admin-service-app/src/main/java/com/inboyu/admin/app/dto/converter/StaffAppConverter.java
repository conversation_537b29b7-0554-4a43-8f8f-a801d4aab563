package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.request.staff.StaffRequestDTO;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.staff.model.*;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.dto.request.StaffDeptRequestDTO;
import com.inboyu.admin.dto.response.*;
import com.inboyu.admin.infrastructure.constant.StaffStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 11:40
 */
@Component
public class StaffAppConverter {

    /**
     * 转换为员工领域对象
     *
     * @param staffId
     * @param dto     请求DTO
     * @return {@link Staff }
     * <AUTHOR>
     * @date 2025/08/05 11:40:47
     */
    public Staff toStaff(StaffId staffId, StaffRequestDTO dto) {
        return Staff.builder()
                .id(staffId)
                .name(dto.getName())
                .phone(PhoneNumber.of(dto.getPhone()))
                .createTime(LocalDateTime.now())
                .status(StaffStatus.create(dto.getStatus().getCode(), dto.getStatus().getTitle()))
                .build();
    }

    /**
     * 转换为员工部门关联对象
     *
     * @param staffId 员工ID
     * @param dto     部门请求DTO
     * @return {@link StaffDept }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:42:00
     */
    public StaffDept toDeptStaff(StaffId staffId, StaffDeptRequestDTO dto) {
        StaffDept.StaffDeptBuilder builder = StaffDept.builder()
                .staffId(staffId)
                .deptId(DeptId.of(Long.valueOf(dto.getDeptId())))
                .includeAll(dto.getIncludeAll());

        // 转换门店ID列表
        if (dto.getStoreIds() != null) {
            builder.storeIds(dto.getStoreIds().stream()
                    .map(storeId -> StoreId.of(Long.valueOf(storeId)))
                    .toList());
        }
        // 转换角色ID列表
        if (dto.getRoleIds() != null) {
            builder.roleIds(dto.getRoleIds().stream()
                    .map(roleId -> RoleId.of(Long.valueOf(roleId)))
                    .toList());
        }
        // 转换过期时间
        if (StringUtils.isNotBlank(dto.getExpireTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            builder.expireTime(LocalDateTime.parse(dto.getExpireTime(), formatter));
        }
        return builder.build();
    }

    /**
     * 转换为新增员工响应DTO
     *
     * @param staff      员工
     * @param staffDepts 员工部门关联列表
     * @return {@link StaffDTO }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:43:00
     */
    public StaffDTO toStaffAddResponseDTO(Staff staff, List<StaffDept> staffDepts) {
        StaffDTO dto = new StaffDTO();
        dto.setStaffId(staff.getId().getValue().toString());
        dto.setUserId(staff.getUserId().getValue().toString());
        dto.setName(staff.getName());
        dto.setPhone(staff.getPhone().getValue());
        // 转换状态
        if (staff.getStatus() != null) {
            StaffStatusDTO statusDTO = new StaffStatusDTO();
            statusDTO.setCode(staff.getStatus().getCode());
            statusDTO.setTitle(staff.getStatus().getValue());
            dto.setStatus(statusDTO);
        }
        // 转换部门信息
        if (staffDepts != null) {
            List<StaffDeptRequestDTO> deptDtos = staffDepts.stream()
                    .map(this::toStaffDeptRequestDTO)
                    .toList();
            dto.setDepts(deptDtos);
        }
        return dto;
    }

    /**
     * 转换为部门请求DTO
     *
     * @param staffDept 员工部门关联
     * @return {@link StaffDeptRequestDTO }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:44:00
     */
    private StaffDeptRequestDTO toStaffDeptRequestDTO(StaffDept staffDept) {
        StaffDeptRequestDTO dto = new StaffDeptRequestDTO();
        dto.setDeptId(staffDept.getDeptId().getValue().toString());
        dto.setIncludeAll(staffDept.getIncludeAll());

        // 转换门店ID列表
        if (staffDept.getStoreIds() != null) {
            dto.setStoreIds(staffDept.getStoreIds().stream()
                    .map(storeId -> storeId.getValue().toString())
                    .toList());
        }
        // 转换角色ID列表
        if (staffDept.getRoleIds() != null) {
            dto.setRoleIds(staffDept.getRoleIds().stream()
                    .map(roleId -> roleId.getValue().toString())
                    .toList());
        }
        // 转换过期时间
        if (staffDept.getExpireTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            dto.setExpireTime(formatter.format(staffDept.getExpireTime()));
        }
        return dto;
    }

    /**
     * @description: 转换StaffDeptRequestDTO为StaffDept
     * @author: zhouxin
     * @date: 2025/8/5 18:20
     * @param: [staffRoleResult]
     * @return: com.inboyu.admin.app.dto.response.staff.StaffDeptResponseDTO
     **/
    private StaffDeptResponseDTO toStaffDeptResponseDTO(StaffRoleResult staffRoleResult) {
        StaffDeptResponseDTO dto = new StaffDeptResponseDTO();
        dto.setDeptId(staffRoleResult.getDeptId().getValue().toString());
        dto.setTitle(staffRoleResult.getTitle());
        dto.setIncludeAll(staffRoleResult.getIncludeAll());
        dto.setStores(staffRoleResult.getStores().stream().map(store -> {
            StaffStoreDTO staffStoreDTO = new StaffStoreDTO();
            staffStoreDTO.setStoreId(store.getId().getValue().toString());
            staffStoreDTO.setTitle(store.getTitle());
            return staffStoreDTO;
        }).toList());
        dto.setRoles(staffRoleResult.getRoles().stream().map(role -> {
            StaffRoleDTO staffRoleDTO = new StaffRoleDTO();
            staffRoleDTO.setRoleId(role.getId().getValue().toString());
            staffRoleDTO.setTitle(role.getTitle());
            return staffRoleDTO;
        }).toList());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        dto.setExpireTime(formatter.format(staffRoleResult.getExpireTime()));
        return dto;
    }

    /**
     * @description: 将Staff对象转换为StaffDetailResponseDTO对象
     * @author: zhouxin
     * @date: 2025/8/5 16:22
     * @param: [staff, resultList]
     * @return: com.inboyu.admin.app.dto.response.staff.StaffDetailResponseDTO
     **/
    public StaffDetailResponseDTO toStaffDetailResponseDTO(Staff staff, List<StaffRoleResult> resultList) {
        StaffDetailResponseDTO staffDetailResponseDTO = new StaffDetailResponseDTO();
        staffDetailResponseDTO.setStaffId(staff.getId().getValue().toString());
        staffDetailResponseDTO.setUserId(staff.getUserId().getValue().toString());
        staffDetailResponseDTO.setName(staff.getName());
        staffDetailResponseDTO.setPhone(staff.getPhone().getValue());
        StaffStatusDTO staffStatusDTO = new StaffStatusDTO();
        staffStatusDTO.setCode(staff.getStatus().getCode());
        staffStatusDTO.setTitle(StaffStatusEnum.titleFromCode(staff.getStatus().getCode()));
        staffDetailResponseDTO.setStatus(staffStatusDTO);
        staffDetailResponseDTO.setCreateTime(staff.getCreateTime());
        staffDetailResponseDTO.setDepts(resultList.stream().map(this::toStaffDeptResponseDTO).toList());
        return staffDetailResponseDTO;
    }
}
