package com.inboyu.admin.app.dto.response.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:36
 */
@Data
public class DeptUpdateResponseDTO {
    @Schema(description = "组织id")
    private String deptId;
    @Schema(description = "组织名称")
    private String title;
    @Schema(description = "上级组织")
    private ParentDeptResponseDTO dept;

    @Data
    public static class ParentDeptResponseDTO {
        @Schema(description = "上级部门ID")
        private String deptId;
        @Schema(description = "上级部门名称")
        private String title;
    }
}
