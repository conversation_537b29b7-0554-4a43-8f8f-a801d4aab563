package com.inboyu.admin.app.dto.response.customer;

import com.inboyu.admin.domain.customer.model.Customer;
import com.inboyu.admin.domain.customer.model.CustomerReal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CustomerResponseDTO implements Serializable {

    /**
     * 租赁人id
     */
    @Schema(description = "租赁人id", example = "1")
    private Long customerId;

    /**
     * 租赁人姓名
     */
    @Schema(description = "租赁人姓名", example = "王五")
    private String name;

    /**
     * 租赁人手机号
     */
    @Schema(description = "租赁人手机号", example = "13889890909")
    private String mobile;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型", example = "certificate_type.Mainland_ID_card")
    private String certificateType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码", example = "387520200709145588")
    private String certificateNumber;

    public static CustomerResponseDTO of(Customer customer, CustomerReal customerReal) {
        CustomerResponseDTO customerResponseDTO = new CustomerResponseDTO();
        customerResponseDTO.setCustomerId(customer.getCustomerId().getValue());
        customerResponseDTO.setName(customer.getName());
        customerResponseDTO.setMobile(customer.getMobile());
        customerResponseDTO.setCertificateType(customerReal.getCertificateType().getCode());
        customerResponseDTO.setCertificateNumber(customerReal.getCertificateNumber().getValue());
        return customerResponseDTO;
    }

    public static List<CustomerResponseDTO> toCustomerResponseDTOList(List<Customer> customerList, List<CustomerReal> customerRealList) {
        Map<Long, CustomerReal> customerRealMap = new HashMap<>();
        for (CustomerReal customerReal : customerRealList) {
            customerRealMap.put(customerReal.getCustomerId().getValue(), customerReal);
        }

        List<CustomerResponseDTO> customerResponseDTOList = new ArrayList<>();
        for (Customer customer : customerList) {
            CustomerReal matchedReal = customerRealMap.get(customer.getCustomerId().getValue());
            customerResponseDTOList.add(of(customer, matchedReal));
        }

        return customerResponseDTOList;
    }

    public static CustomerResponseDTO of(Customer customer) {
        CustomerResponseDTO customerResponseDTO = new CustomerResponseDTO();
        customerResponseDTO.setCustomerId(customer.getCustomerId().getValue());
        customerResponseDTO.setName(customer.getName());
        customerResponseDTO.setMobile(customer.getMobile());
        return customerResponseDTO;
    }

    public static List<CustomerResponseDTO> toCustomerResponseDTOList(List<Customer> customerList) {
        List<CustomerResponseDTO> customerResponseDTOList = new ArrayList<>();
        for (Customer customer : customerList) {
            customerResponseDTOList.add(of(customer));
        }
        return customerResponseDTOList;
    }
}
