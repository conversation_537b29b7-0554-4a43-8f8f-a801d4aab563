package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.request.operationlog.OperationLogCollectRequestDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogCollectResponseDTO;
import com.inboyu.admin.app.dto.response.operationlog.OperationLogPageResponseDTO;
import com.inboyu.admin.domain.operationlog.model.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 操作日志请求与领域对象互转
 * <AUTHOR>
 */
@Component
public class OperationLogAppConverter {

    /**
     * 操作日志收集请求对象转换为操作日志领域对象
     *
     * @param operationId
     * @param dto
     * @return {@link OperationLog }
     */
    public OperationLog toOperationLog(OperationId operationId, OperationLogCollectRequestDTO dto) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime operationTime = LocalDateTime.parse(dto.getOperationTime(), formatter);
        return OperationLog.of(operationId, dto.getStoreId() == null ? null : StoreId.of(dto.getStoreId()), StaffId.of(dto.getStaffId()),
                dto.getOperator(),operationTime, OperationType.of(dto.getOperationType(),null),
                dto.getTitle(), dto.getContent(), Attachment.of(dto.getAttachmentName(), dto.getAttachmentUrl()),
                dto.getSuccess(), dto.getFailReason(),BizType.of(dto.getBizType(),null));
    }

    /**
     * 操作日志领域对象转换为操作日志收集响应对象
     *
     * @param operationLog
     * @return {@link OperationLogCollectResponseDTO }
     */
    public OperationLogCollectResponseDTO toOperationLogCollectResponseDTO(OperationLog operationLog) {
        OperationLogCollectResponseDTO dto = new OperationLogCollectResponseDTO();
        dto.setStaffId(String.valueOf(operationLog.getStaffId().getValue()));
        dto.setOperator(operationLog.getOperator());
        dto.setOperationTime(operationLog.getOperationTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setOperationType(operationLog.getOperationType().getCode());
        dto.setTitle(operationLog.getTitle());
        dto.setContent(operationLog.getContent());
        dto.setAttachmentName(operationLog.getAttachment().getName());
        dto.setAttachmentUrl(operationLog.getAttachment().getUrl());
        dto.setSuccess(operationLog.getSuccess());
        dto.setFailReason(operationLog.getFailReason());
        dto.setBizType(operationLog.getBizType().getCode());
        return dto;
    }

    /**
     * 操作日志领域对象转换为操作日志查询响应对象
     *
     * @param operationLog
     * @return {@link OperationLogPageResponseDTO }
     */
    public OperationLogPageResponseDTO toOperationLogResponseDTO(OperationLog operationLog) {
        OperationLogPageResponseDTO dto = new OperationLogPageResponseDTO();
        dto.setStaffId(String.valueOf(operationLog.getStaffId().getValue()));
        dto.setOperator(operationLog.getOperator());
        dto.setOperationTime(operationLog.getOperationTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setOperationType(operationLog.getOperationType().getCode());
        dto.setTitle(operationLog.getTitle());
        dto.setContent(operationLog.getContent());
        dto.setAttachmentName(operationLog.getAttachment().getName());
        dto.setAttachmentUrl(operationLog.getAttachment().getUrl());
        dto.setSuccess(operationLog.getSuccess());
        dto.setFailReason(operationLog.getFailReason());
        dto.setBizType(operationLog.getBizType().getCode());
        return dto;
    }
}
