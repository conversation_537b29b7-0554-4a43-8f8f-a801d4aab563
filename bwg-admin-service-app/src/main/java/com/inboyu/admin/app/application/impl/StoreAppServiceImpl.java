package com.inboyu.admin.app.application.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.inboyu.admin.app.application.StoreAppService;
import com.inboyu.admin.app.dto.converter.StoreAppConverter;
import com.inboyu.admin.app.dto.request.store.StoreCreateRequestDTO;
import com.inboyu.admin.app.dto.request.store.StoreUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.store.StoreCreateResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreDTO;
import com.inboyu.admin.app.dto.response.store.StoreUpdateResponseDTO;
import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.service.DeptDomainService;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.domain.store.repository.StoreRepository;
import com.inboyu.admin.domain.store.service.StoreDomainService;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR> Dong
 * @date 2025年08月02日 11:23
 */
@Service
@Log4j2
public class StoreAppServiceImpl implements StoreAppService {

    @Autowired
    private StoreDomainService storeDomainService;

    @Autowired
    private DeptDomainService deptDomainService;

    @Autowired
    private StoreAppConverter storeAppConverter;

    @Autowired
    private StoreRepository storeRepository;

    /**
     * 添加门店
     *
     * @param dto
     * @return {@link StoreCreateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 11:23:56
     */
    @Override
    @DistributedLock(key = "#dto.deptId", transType = "locker_createStore_")
    public StoreCreateResponseDTO createStore(StoreCreateRequestDTO dto) {
        Dept dept = deptDomainService.findByDeptId(DeptId.of(Long.valueOf(dto.getDeptId())));
        if (null == dept) {
            log.error("添加门店失败,原因:门店所属组织不存在,门店名称:{},组织id:{}", dto.getTitle(), dto.getDeptId());
            throw new AppException(ResponseCode.STORE_DEPT_NOT_EXIST);
        }
        StoreId storeId = storeDomainService.generateId();
        Store store = storeAppConverter.toStore(storeId, dto);
        Store createStore = storeDomainService.createStore(store);
        return storeAppConverter.toCreateDTO(dept, createStore);
    }

    /**
     * 查询门店详情
     *
     * @param storeId
     * @return {@link StoreDetailResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    public StoreDetailResponseDTO getStore(Long storeId) {
        // 1.查询门店信息
        Store store = storeDomainService.findByStoreId(StoreId.of(storeId));
        if (null == store) {
            log.error("查询门店详情失败,原因:门店不存在,门店id:{}", storeId);
            throw new AppException(ResponseCode.STORE_NOT_EXIST);
        }
        // 2.查询门店所属组织信息
        Dept dept = deptDomainService.findByDeptId(store.getDeptId());
        if (null == dept) {
            log.error("查询门店详情失败,原因:门店所属组织不存在,门店id:{},组织id:{}", storeId, store.getDeptId().getValue());
            throw new AppException(ResponseCode.STORE_DEPT_NOT_EXIST);
        }
        // 3.转换并返回门店详情
        return storeAppConverter.toDetailDTO(dept, store);
    }

    /**
     * 更新门店
     *
     * @param storeId
     * @param dto
     * @return {@link StoreUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    @DistributedLock(key = "#storeId", transType = "locker_updateStore_")
    public StoreUpdateResponseDTO updateStore(Long storeId, StoreUpdateRequestDTO dto) {
        // 1.校验门店是否存在
        Store existingStore = storeDomainService.findByStoreId(StoreId.of(storeId));
        if (null == existingStore) {
            log.error("更新门店失败,原因:门店不存在,门店id:{}", storeId);
            throw new AppException(ResponseCode.STORE_NOT_EXIST);
        }
        // 2.校验门店所属组织是否存在
        Dept dept = deptDomainService.findByDeptId(DeptId.of(Long.valueOf(dto.getDeptId())));
        if (null == dept) {
            log.error("更新门店失败,原因:门店所属组织不存在,门店名称:{},组织id:{}", dto.getTitle(), dto.getDeptId());
            throw new AppException(ResponseCode.STORE_DEPT_NOT_EXIST);
        }
        // 3.转换并更新门店
        Store updateStore = storeAppConverter.toStore(StoreId.of(storeId), dto);
        storeDomainService.updateStore(updateStore);
        // 4.直接使用更新后的Store返回，避免重复查询
        return storeAppConverter.toUpdateDTO(dept, updateStore);
    }

    /**
     * 分页列出门店
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param keywords 门店名称关键词(支持模糊)
     * @param deptId
     * @return {@link Pagination }<{@link StoreDTO }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:38:24
     */
    @Override
    public Pagination<StoreDTO> pageStores(Integer pageNum, Integer pageSize, String keywords, Long deptId) {
        // 1. 如果指定了组织ID，查询该组织及其所有子组织
        List<DeptId> deptIds = new ArrayList<>();
        if (deptId != null) {
            deptIds.add(DeptId.of(deptId));
            List<Dept> allChildDept = deptDomainService.findAllChildDept(DeptId.of(deptId));
            List<DeptId> allDeptIds = allChildDept.stream().map(Dept::getId).toList();
            deptIds.addAll(allDeptIds);
        }
        // 2. 分页查询门店（支持多组织ID）
        Pagination<Store> stores = storeDomainService.pageStores(pageNum, pageSize, keywords, deptIds);
        // 3. 转换为DTO
        List<StoreDTO> storeDTOs = stores.getList().stream()
                .map(storeAppConverter::toDTO).toList();
        // 4. 构造分页结果
        return new Pagination<>(
                stores.getPageNum(),
                stores.getPageSize(),
                stores.getTotalPages(),
                stores.getTotal(),
                storeDTOs);
    }

    @Override
    public List<StoreDTO> filterByStoreIds(String[] store) {
        List<StoreId> storeIds = Arrays.stream(store).map(StoreId::of).toList();
        List<Store> stores = storeRepository.findByStoreIds(storeIds);
        return stores.stream().map(storeAppConverter::toDTO).toList();
    }
}
