package com.inboyu.admin.app.application.impl;

import com.inboyu.admin.app.application.DeptAppService;
import com.inboyu.admin.app.dto.converter.DeptAppConverter;
import com.inboyu.admin.app.dto.request.dept.DeptCreateRequestDTO;
import com.inboyu.admin.app.dto.response.dept.DeptCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptListResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptUpdateResponseDTO;
import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptCreationResult;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.model.DeptUpdateResult;
import com.inboyu.admin.domain.dept.service.DeptDomainService;
import com.inboyu.admin.domain.staff.service.StaffRoleDomainService;
import com.inboyu.admin.domain.store.service.StoreDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年07月29日 14:58
 */
@Log4j2
@Service
public class DeptAppServiceImpl implements DeptAppService {
    @Autowired
    private DeptDomainService deptDomainService;

    @Autowired
    private StoreDomainService storeDomainService;

    @Autowired
    private DeptAppConverter deptAppConverter;

    @Autowired
    private StaffRoleDomainService staffRoleDomainService;

    /**
     * 添加组织
     *
     * @param dto
     * @return {@link DeptCreateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/07/30 09:16:26
     */
    @Override
    @DistributedLock(key = "#dto.parentDeptId", transType = "locker_createDept_")
    public DeptCreateResponseDTO createDept(DeptCreateRequestDTO dto) {
        DeptId parentDeptId = DeptId.of(Long.parseLong(dto.getParentDeptId()));
        // 1.创建组织
        DeptCreationResult deptResult = deptDomainService.createDept(parentDeptId, dto.getTitle());
        // 2.封装返回
        return deptAppConverter.toDeptAddResponseDTO(deptResult.getCreatedDept(), deptResult.getParentDept());
    }

    /**
     * 编辑组织
     *
     * @param deptId
     * @param title
     * @return {@link DeptUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:40:00
     */
    @Override
    @DistributedLock(key = "#deptId", transType = "locker_updateDept_")
    public DeptUpdateResponseDTO updateDept(Long deptId, String title) {
        DeptId deptIdObj = DeptId.of(deptId);
        // 1. 更新组织
        DeptUpdateResult deptResult = deptDomainService.updateDept(deptIdObj, title);
        // 2. 封装返回
        return deptAppConverter.toDeptUpdateResponseDTO(deptResult.getUpdatedDept(), deptResult.getParentDept());
    }

    /**
     * 列出组织
     *
     * @return {@link DeptListResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/07/30 14:08:23
     */
    @Override
    public DeptListResponseDTO listDept() {
        // 1. 查询根组织及其所有子组织
        List<Dept> allDept = deptDomainService.findAllDept();
        // 2. 构建树形结构
        return deptAppConverter.buildDeptTree(allDept);
    }

    /**
     * 删除组织
     *
     * @param deptId
     * <AUTHOR> Dong
     * @date 2025/08/04 15:47:11
     */
    @Override
    public void deleteDept(Long deptId) {
        DeptId id = DeptId.of(deptId);
        Dept dept = deptDomainService.findByDeptId(id);
        if (null == dept) {
            log.error("删除组织失败,原因:组织不存在,组织id:{}", deptId);
            throw new AppException(ResponseCode.DEPT_NOT_EXIST);
        }
        // 1. 查询组织所有子组织
        List<Dept> allChildDept = deptDomainService.findAllChildDept(id);
        List<DeptId> deptIds = new ArrayList<>();
        deptIds.add(id);
        if (ObjectUtils.isNotEmpty(allChildDept)) {
            deptIds.addAll(allChildDept.stream().map(Dept::getId).toList());
        }
        // 2.批量查询组织下门店数
        Integer storeCount = storeDomainService.countByDeptIds(deptIds);
        if (storeCount > 0) {
            log.error("删除组织失败,原因:组织下有门店,组织id:{}", deptId);
            throw new AppException(ResponseCode.DEPT_DELETE_ERROR);
        }
        // 3.查询组织下员工数,存在员工则不允许删除
        Integer staffCount = staffRoleDomainService.countByDeptIds(deptIds);
        if (staffCount > 0) {
            log.error("删除组织失败,原因:组织下有员工,组织id:{}", deptId);
            throw new AppException(ResponseCode.DEPT_DELETE_ERROR);
        }
        deptDomainService.deleteDepts(deptIds);
    }
}
