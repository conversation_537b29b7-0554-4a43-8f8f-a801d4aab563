package com.inboyu.admin.app.application;

import com.inboyu.admin.app.dto.request.staff.StaffRequestDTO;
import com.inboyu.admin.dto.response.StaffDTO;
import com.inboyu.admin.dto.response.StaffDetailResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuPermissionsResponseDTO;

import java.util.List;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

/**
 * <AUTHOR>
 * @date 2025年08月05日 11:23
 */
public interface StaffAppService {
    /**
     * 创建员工
     *
     * @param dto
     * @return {@link StaffDTO }
     * <AUTHOR>
     * @date 2025/08/05 11:24:47
     */
    StaffDTO createStaff(StaffRequestDTO dto);

    /**
     * @description: 获取员工详情
     * @author: zhouxin
     * @date: 2025/8/5 15:53
     * @param: [staffId] 员工id
     * @return: com.inboyu.admin.app.dto.response.staff.StaffDetailResponseDTO
     **/
    StaffDetailResponseDTO getStaffDetail(Long staffId);

    /**
     * 员工运营系统菜单及权限
     *
     * @param staffId
     * @return {@link List }<{@link StaffMenuPermissionsResponseDTO }>
     * <AUTHOR> Dong
     * @date 2025/08/06 09:22:27
     */
    List<StaffMenuPermissionsResponseDTO> getStaffPermissions(Long staffId);

    /**
     * @description: 根据条件分页查询员工列表
     * @author: zhouxin
     * @date: 2025/8/5 15:53
     * @param: [pageNum, pageSize, keyword, status, roleId] 当前页码，每页大小，关键字，状态，角色id
     * @return: com.inboyu
     * @author: zhouxin
     * @date: 2025/8/5 20:00
     * @param: [pageNum, pageSize, keyword, status, roleId]
     * @return: com.inboyu.spring.cloud.starter.common.dto.Pagination<com.inboyu.admin.app.dto.response.staff.StaffDetailResponseDTO>
     **/
    Pagination<StaffDetailResponseDTO> pageByKeywordLike(Integer pageNum, Integer pageSize, String keyword, String status, String roleId, String deptId);

    /**
     * 更新员工
     *
     * @param aLong
     * @param dto
     * @return {@link StaffDTO }
     * <AUTHOR> Dong
     * @date 2025/08/06 15:10:20
     */
    StaffDTO updateStaff(Long aLong, StaffRequestDTO dto);

    /**
     * 获取所有角色
     */
    List<StaffMenuPermissionsResponseDTO> getAllPermission();
}
