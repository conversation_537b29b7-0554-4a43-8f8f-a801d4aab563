package com.inboyu.admin.app.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.inboyu.admin.app.application.CustomerWxAppService;
import com.inboyu.admin.app.dto.request.customer.CustomerWxSaveDTO;
import com.inboyu.admin.app.dto.request.customer.CustomerWxUpdateDTO;
import com.inboyu.admin.app.dto.response.customer.CustomerWxResponseDTO;

@RestController
@RequestMapping("/api/v1/wx/customers")
public class CustomerWxController {

    @Autowired
    private CustomerWxAppService customerWxAppService;

    @RequestMapping("/save")
    public CustomerWxResponseDTO save(@RequestBody @Validated CustomerWxSaveDTO customerWxSaveDTO) {

        return customerWxAppService.save(customerWxSaveDTO);
    }

    @RequestMapping("/update")
    public CustomerWxResponseDTO update(@RequestBody @Validated CustomerWxUpdateDTO customerWxUpdateDTO) {

        return customerWxAppService.update(customerWxUpdateDTO);
    }

}
