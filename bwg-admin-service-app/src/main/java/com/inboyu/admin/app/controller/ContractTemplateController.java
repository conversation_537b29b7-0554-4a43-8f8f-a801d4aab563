package com.inboyu.admin.app.controller;

import com.inboyu.admin.app.application.ContractTemplateAppService;
import com.inboyu.admin.app.dto.request.contracttemplate.ContractTemplateSaveRequestDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateGetResponseDTO;
import com.inboyu.admin.app.dto.response.contracttemplate.ContractTemplateSaveResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name="合同模板")
@RestController
@RequestMapping("/api/v1/contract-template")
public class ContractTemplateController {
    @Autowired
    private ContractTemplateAppService contractTemplateAppService;

    @GetMapping
    @Operation(summary = "获取门店合同模板")
    public ContractTemplateGetResponseDTO get(@Parameter(description = "门店Id",required = true) @RequestParam String storeId) {
        return contractTemplateAppService.get(storeId);
    }

    @PostMapping
    @Operation(summary = "新增/编辑门店合同模板")
    public ContractTemplateSaveResponseDTO save(@Parameter(description = "新增/编辑合同模板请求实体",required = true) @Valid @RequestBody ContractTemplateSaveRequestDTO dto) {
        return contractTemplateAppService.save(dto);
    }
}
