package com.inboyu.admin.app.dto.converter;

import com.inboyu.admin.app.dto.response.dict.DictCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictDetailResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictListResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictUpdateResponseDTO;
import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCreationResult;
import com.inboyu.admin.domain.dict.model.DictUpdateResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 15:46
 */
@Component
public class DictAppConverter {

    /**
     * 转换为新增字典响应DTO
     *
     * @param dict 新增字典
     * @return {@link DictCreateResponseDTO }
     * <AUTHOR>
     * @date 2025/08/04 14:45:00
     */
    public DictCreateResponseDTO toDictCreateResponseDTO(DictCreationResult dict) {
        DictCreateResponseDTO dictCreateResponseDTO = new DictCreateResponseDTO();
        dictCreateResponseDTO.setCode(dict.getCreateDict().getCode().getValue());
        dictCreateResponseDTO.setTitle(dict.getCreateDict().getTitle());
        dictCreateResponseDTO.setIcon(dict.getCreateDict().getIcon());
        dictCreateResponseDTO.setRemark(dict.getCreateDict().getRemark());
        dictCreateResponseDTO.setIndex(dict.getCreateDict().getIndex());
        return dictCreateResponseDTO;
    }

    /**
     * 转换为更新字典响应DTO
     *
     * @param dict 更新字典
     * @return {@link DictUpdateResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:45:00
     */
    public DictUpdateResponseDTO toDictUpdateResponseDTO(DictUpdateResult dict) {
        DictUpdateResponseDTO dictUpdateResponseDTO = new DictUpdateResponseDTO();
        dictUpdateResponseDTO.setCode(dict.getUpdateDict().getCode().getValue());
        dictUpdateResponseDTO.setTitle(dict.getUpdateDict().getTitle());
        dictUpdateResponseDTO.setIcon(dict.getUpdateDict().getIcon());
        dictUpdateResponseDTO.setRemark(dict.getUpdateDict().getRemark());
        dictUpdateResponseDTO.setIndex(dict.getUpdateDict().getIndex());
        return dictUpdateResponseDTO;
    }

    /**
     * 转换为字典列表响应DTO
     *
     * @param dictList 字典列表
     * @return {@link DictListResponseDTO }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:45:00
     */
    public DictListResponseDTO toDictListResponseDTO(List<Dict> dictList) {
        DictListResponseDTO dictListResponseDTO = new DictListResponseDTO();
        dictListResponseDTO.setDtoList(dictList.stream().map(dict -> {
            DictDetailResponseDTO dictDetailResponseDTO = new DictDetailResponseDTO();
            dictDetailResponseDTO.setCode(dict.getCode().getValue());
            dictDetailResponseDTO.setTitle(dict.getTitle());
            dictDetailResponseDTO.setIcon(dict.getIcon());
            dictDetailResponseDTO.setRemark(dict.getRemark());
            dictDetailResponseDTO.setIndex(dict.getIndex());
            return dictDetailResponseDTO;
        }).collect(Collectors.toList()));
        return dictListResponseDTO;
    }
}
