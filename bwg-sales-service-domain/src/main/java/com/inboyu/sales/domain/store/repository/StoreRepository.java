package com.inboyu.sales.domain.store.repository;

import com.inboyu.sales.domain.store.model.Store;

import java.util.List;

/**
 * 门店仓储接口
 */
public interface StoreRepository {

    /**
     * 根据门店ID获取门店信息
     * @param storeId 门店ID
     * @return 门店信息
     */
    Store getStore(String storeId);

    /**
     * 根据若干门店ID获取门店信息
     *
     * @param storeIds 门店IDs
     * @return 门店list信息
     */
    List<Store> filterByStoreIds(String[] storeIds);
}
