package com.inboyu.sales.domain.reserve.model;

import com.inboyu.spring.cloud.starter.common.exception.AppException;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 户型ID值对象
 */
@Getter
@EqualsAndHashCode
public class RoomTypeId {

    private final Long value;

    private RoomTypeId(Long value) {
        this.value = value;
    }

    public static RoomTypeId of(Long value) {
        return new RoomTypeId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
