package com.inboyu.sales.domain.reserve.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约看房实体
 */
@Getter
@Setter
@Builder
public class ReserveSeeHouse {

    /**
     * 自增ID，无业务含义
     */
    private Long id;  // 修复：原来错误地使用了StoreId类型

    /**
     * 预约看房记录id
     */
    private ReserveSeeHouseId reserveSeeHouseId;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 客户ID
     */
    private CustomerId customerId;

    /**
     * 员工ID
     */
    private StaffId staffId;

    /**
     * 门店ID
     */
    private StoreId storeId;

    /**
     * 户型ID
     */
    private RoomTypeId roomTypeId;

    /**
     * 房间ID
     */
    private RoomId roomId;

    /**
     * 预约日期
     */
    private LocalDate reserveDate;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 看房状态
     */
    private String seeState;


    /**
     * 获取预约状态
     */
    public SeeHouseStatus getStatus() {
        return SeeHouseStatus.fromCode(this.seeState);
    }

    /**
     * 取消预约
     */
    public void cancel() {
        this.seeState = SeeHouseStatus.CANCELED.getCode();
    }

    /**
     * 检查时间段是否冲突
     */
    public boolean isTimeConflict(LocalTime newStartTime, LocalTime newEndTime) {
        return !(newEndTime.isBefore(this.startTime) || newStartTime.isAfter(this.endTime));
    }

    /**
     * 更新预约时间
     */
    public void updateTime(LocalTime newStartTime, LocalTime newEndTime) {
        this.startTime = newStartTime;
        this.endTime = newEndTime;
    }

    /**
     * 检查是否正确的时间
     * @param startTime
     * @param endTime
     * @return
     */
    public boolean isRightTime(LocalTime startTime, LocalTime endTime){
        return endTime.isBefore(startTime);
    }


}
