package com.inboyu.sales.domain.customer.service.impl;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.repository.CustomerListRepository;
import com.inboyu.sales.domain.customer.service.CustomerListService;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户列表领域服务实现
 */
@Service
public class CustomerListServiceImpl implements CustomerListService {

    @Autowired
    private CustomerListRepository customerListRepository;

    @Override
    public Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, StoreId storeId) {

        // 业务规则：参数校验
        if (storeId == null) {
            throw new IllegalArgumentException("门店ID不能为空");
        }
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100; // 业务规则：限制最大页面大小，防止性能问题
        }

        return customerListRepository.pageCustomerList(pageNum, pageSize, storeId);
    }
}
