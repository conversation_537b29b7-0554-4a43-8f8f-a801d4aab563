package com.inboyu.sales.domain.customer.model;

import com.inboyu.sales.domain.store.model.StoreId;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 客户列表项领域模型
 */
@Getter
@Setter
@Builder
public class CustomerListItem {

    /**
     * 平台客户ID
     */
    private PlatformCustomerId platformCustomerId;

    /**
     * 客户ID
     */
    private CustomerId customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 门店ID
     */
    private StoreId storeId;

    /**
     * 销售周期开始时间
     */
    private LocalDateTime saleCycleStartTime;


    /**
     * 预约看房记录
     */
    private CustomerReservation reservation;
}
