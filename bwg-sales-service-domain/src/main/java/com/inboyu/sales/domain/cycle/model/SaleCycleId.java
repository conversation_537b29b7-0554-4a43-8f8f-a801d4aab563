package com.inboyu.sales.domain.cycle.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 销售周期ID值对象
 */
@Getter
@EqualsAndHashCode
public class SaleCycleId {

    private final Long value;

    private SaleCycleId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("销售周期ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static SaleCycleId of(Long value) {
        return new SaleCycleId(value);
    }
}
