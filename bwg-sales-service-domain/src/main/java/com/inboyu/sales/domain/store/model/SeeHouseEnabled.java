package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
@Builder
public class SeeHouseEnabled {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public final static String STATUS_ENABLED = "status.enabled";
    public final static String STATUS_DISABLED = "status.disabled";

    public boolean isEnabled() {
        return STATUS_ENABLED.equals(this.code);
    }
}
