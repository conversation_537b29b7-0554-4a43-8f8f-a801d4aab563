package com.inboyu.sales.domain.cycle.service.impl;

import com.inboyu.sales.domain.behavior.model.BehaviorEvent;
import com.inboyu.sales.domain.behavior.model.BusinessId;
import com.inboyu.sales.domain.behavior.model.EnventType;
import com.inboyu.sales.domain.behavior.repository.BehaviorEventRepository;
import com.inboyu.sales.domain.customer.model.CustomerId;
import com.inboyu.sales.domain.customer.model.CustomerStage;
import com.inboyu.sales.domain.customer.model.PlatformCustomer;
import com.inboyu.sales.domain.customer.repository.PlatformCustomerRepository;
import com.inboyu.sales.domain.cycle.model.SaleCycle;
import com.inboyu.sales.domain.cycle.model.SaleCycleStatus;
import com.inboyu.sales.domain.cycle.repository.SaleCycleRepository;
import com.inboyu.sales.domain.cycle.service.SalesCycleManagementDomainService;
import com.inboyu.sales.domain.reserve.model.StaffId;
import com.inboyu.sales.domain.store.model.StoreId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class SalesCycleManagementDomainServiceImpl implements SalesCycleManagementDomainService {

    @Autowired
    private BehaviorEventRepository behaviorEventRepository;

    @Autowired
    private PlatformCustomerRepository platformCustomerRepository;

    @Autowired
    private SaleCycleRepository saleCycleRepository;



    @Override
    public void manageSalesCycle(Long customerId, Long storeId, Long businessId, String eventType) {
        // 第二步：创建行为事件
        createBehaviorEvent(businessId, eventType,customerId,storeId);

        // 第三步：查询或创建平台客户 Todo:待从用户服务通过customerId查询
        PlatformCustomer platformCustomer = ensurePlatformCustomer(customerId, customerId.toString() + "测试姓名");

        // 第四步：查询或创建/更新平台级销售周期
        SaleCycle platformSaleCycle = ensureOrUpdatePlatformSaleCycle(platformCustomer.getPlatformCustomerId(),customerId);

        // 第五步：查询或创建/更新门店级销售周期
        ensureOrUpdateStoreSaleCycle(platformCustomer.getPlatformCustomerId(), storeId, platformSaleCycle.getSaleCycleId(),customerId);

        // 第六步：更新平台客户的当前销售周期ID
        updateCurrentSaleCycleId(platformCustomer, platformSaleCycle.getSaleCycleId());
    }

    private void createBehaviorEvent(Long businessId, String eventType,Long customerId,Long storeId) {
        BehaviorEvent event = BehaviorEvent.builder()
                .eventType(EnventType.create(eventType, ""))
                .businessId(BusinessId.of(businessId))
                .customerId(CustomerId.of(customerId))
                .eventContent("预约事件")
                .staffId(StaffId.of(0L))
                .storeId(StoreId.of(storeId))
                .build();
        if (!event.isHasBehaviorEventId()) {
            event.setBehaviorEventId(behaviorEventRepository.generateId());
        }
        behaviorEventRepository.save(event);
    }

    private PlatformCustomer ensurePlatformCustomer(Long customerId, String name) {
        PlatformCustomer existing = platformCustomerRepository.findByCustomerId(customerId);
        if (existing != null) {
            return existing;
        }

        PlatformCustomer newCustomer = PlatformCustomer.builder()
                .customerId(customerId)
                .name(name)
                .currentSaleCycleId(0L)
                .staffId(0L)
                .customerStage(CustomerStage.POTENTIAL.getCode())
                .build();
        return platformCustomerRepository.save(newCustomer);
    }

    private SaleCycle ensureOrUpdatePlatformSaleCycle(Long platformCustomerId,Long customerId) {
        SaleCycle existing = saleCycleRepository.findPlatformSaleCycle(platformCustomerId);
        if (existing != null) {
            // 更新现有记录的时间
            existing.updateCycleTime();
            return saleCycleRepository.save(existing);
        }

        // 创建新的平台级销售周期
        SaleCycle platformSaleCycle = SaleCycle.builder()
                .platformCustomerId(platformCustomerId)
                .storeId(0L) // 平台级为空
                .customerId(customerId)
                .staffId(0L)
                .cycleStatus(SaleCycleStatus.ACTIVE.getCode())
                .deleted(0)
                .platformSaleCycleId(0L)
                .build();

        platformSaleCycle.initializeCycleTime(); // 设置初始时间

        return saleCycleRepository.save(platformSaleCycle);
    }

    private void ensureOrUpdateStoreSaleCycle(Long platformCustomerId, Long storeId, Long platformSaleCycleId,Long customerId) {
        SaleCycle existing = saleCycleRepository.findStoreSaleCycle(platformCustomerId, storeId, platformSaleCycleId);
        if (existing != null) {
            // 更新现有记录的时间
            existing.updateCycleTime();
            saleCycleRepository.save(existing);
            return;
        }

        // 创建新的门店级销售周期
        SaleCycle storeSaleCycle = SaleCycle.builder()
                .platformCustomerId(platformCustomerId)
                .storeId(storeId)
                .platformSaleCycleId(platformSaleCycleId)
                .customerId(customerId)
                .cycleStatus(SaleCycleStatus.ACTIVE.getCode())
                .staffId(0L)
                .deleted(0)
                .build();
        storeSaleCycle.initializeCycleTime(); // 设置初始时间
        saleCycleRepository.save(storeSaleCycle);
    }

    private void updateCurrentSaleCycleId(PlatformCustomer customer, Long saleCycleId) {
        customer.updateCurrentSaleCycleId(saleCycleId);
        platformCustomerRepository.save(customer);
    }
}