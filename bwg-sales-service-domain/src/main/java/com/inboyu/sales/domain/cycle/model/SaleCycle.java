package com.inboyu.sales.domain.cycle.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 销售周期聚合根
 */
@Getter
@Setter
@Builder
public class SaleCycle {

    /**
     * 自增ID，无业务含义
     */
    private Long id;

    /**
     * 销售周期业务ID
     */
    private Long saleCycleId;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 平台客户ID
     */
    private Long platformCustomerId;

    /**
     * 平台销售周期ID
     */
    private Long platformSaleCycleId;

    /**
     * 销售周期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 销售周期结束时间
     */
    private LocalDateTime endTime;

    /**
     * 销售周期回收时间
     */
    private LocalDateTime timeOut;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 销售周期状态
     */
    private String cycleStatus;

    /**
     * 获取销售周期状态
     */
    public SaleCycleStatus getStatus() {
        return SaleCycleStatus.fromCode(this.cycleStatus);
    }

    /**
     * 设置销售周期状态
     */
    public void setStatus(SaleCycleStatus status) {
        this.cycleStatus = status.getCode();
    }


    /**
     * 初始化销售周期时间
     */
    public void initializeCycleTime() {
        this.startTime = LocalDateTime.now();
        this.endTime = this.startTime.plusDays(30);
        this.timeOut = this.startTime.plusYears(10);//暂定
    }

    /**
     * 更新销售周期时间
     */
    public void updateCycleTime() {
       // this.startTime = LocalDateTime.now();

        this.endTime = LocalDateTime.now().plusDays(30);
        this.timeOut = LocalDateTime.now().plusYears(10);
    }

}
