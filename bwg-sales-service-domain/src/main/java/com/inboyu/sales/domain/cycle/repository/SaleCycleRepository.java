// SaleCycleRepository.java
package com.inboyu.sales.domain.cycle.repository;

import com.inboyu.sales.domain.cycle.model.SaleCycle;

public interface SaleCycleRepository {
    /**
     * 查询平台级销售周期
     * @param platformCustomerId 平台客户ID
     * @return 平台级销售周期，不存在返回null
     */
    SaleCycle findPlatformSaleCycle(Long platformCustomerId);

    /**
     * 查询门店级销售周期
     * @param platformCustomerId 平台客户ID
     * @param storeId 门店ID
     * @param platformSaleCycleId 平台销售周期ID
     * @return 门店级销售周期，不存在返回null
     */
    SaleCycle findStoreSaleCycle(Long platformCustomerId, Long storeId, Long platformSaleCycleId);

    /**
     * 保存销售周期
     * @param saleCycle 销售周期
     * @return 保存后的销售周期
     */
    SaleCycle save(SaleCycle saleCycle);
}