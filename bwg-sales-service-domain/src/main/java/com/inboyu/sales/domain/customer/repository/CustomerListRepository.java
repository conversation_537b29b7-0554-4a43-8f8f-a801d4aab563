package com.inboyu.sales.domain.customer.repository;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Repository;

/**
 * 客户列表仓储接口
 * 只负责数据访问，不包含业务逻辑
 */
@Repository
public interface CustomerListRepository {

    /**
     * 分页查询指定门店的客户列表
     * Repository层只负责基础数据查询，业务逻辑由Domain Service处理
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param storeId  门店ID（必填）
     * @return 分页的客户列表
     */
    Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, StoreId storeId);
}
