package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
@Builder
public class SeeHouseValidDay {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7 = "store_sales_config_see_house_valid_day.7";
    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14 = "store_sales_config_see_house_valid_day.14";
    public final static String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30 = "store_sales_config_see_house_valid_day.30";

    //添加 getDays
    public int getDays() {
        switch (this.code) {
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_7:
                return 7;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_14:
                return 14;
            case STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_30:
                return 30;
            default:
                throw new IllegalArgumentException("无效的看房有效天数编码: " + this.code);
        }
    }
}
