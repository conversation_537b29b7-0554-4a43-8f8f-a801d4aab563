package com.inboyu.admin.event;

import com.inboyu.admin.constant.AdminRocketMqTopicConstant;
import com.inboyu.spring.cloud.starter.common.event.MessageEvent;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月02日 09:48
 */
@Data
public class StoreCreateEvent extends MessageEvent {
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 运营商id
     */
    private Long tenantId;

    public StoreCreateEvent() {
        super.setTopic(AdminRocketMqTopicConstant.TOPIC_BWG_TENANT);
        super.setTag(AdminRocketMqTopicConstant.TAG_STORE_CREATED);
    }
}
