package com.inboyu.admin.event;

import com.inboyu.admin.constant.AdminRocketMqTopicConstant;
import com.inboyu.spring.cloud.starter.common.event.MessageEvent;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月05日 16:48
 */
@Data
public class StaffCreateEvent extends MessageEvent {
    /**
     * 运营商ID
     */
    private Long tenantId;
    /**
     * 员工ID
     */
    private Long staffId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 员工状态
     */
    private String status;
    /**
     * 旧用户ID
     */
    private Long oldUserId;

    public StaffCreateEvent() {
        super.setTopic(AdminRocketMqTopicConstant.TOPIC_BWG_TENANT);
        super.setTag(AdminRocketMqTopicConstant.TAG_STAFF_CREATED);
    }

    public boolean isHasOldUserId() {
        return oldUserId != null;
    }

    /**
     * 判断UserId 是否变更了
     * @return
     */
    public boolean isUserIdChange() {
        return userId != null && !userId.equals(oldUserId);
    }
}
