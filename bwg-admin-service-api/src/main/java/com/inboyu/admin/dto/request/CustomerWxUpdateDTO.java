package com.inboyu.admin.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信用户信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerWxUpdateDTO {

    /**
     * 用户唯一标识
     */
    @Schema(description = "用户唯一标识", example = "cs1222")
    @NotBlank(message = "用户唯一标识不能为空")
    private String openId;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID", example = "cs1222")
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13690989098")
    @NotBlank(message = "用户手机号不能为空")
    private String phoneNumber;
}
