package com.inboyu.admin.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerRequestDTO implements Serializable {

    /**
     * 租赁人姓名
     */
    @Schema(description = "租赁人姓名", example = "王五")
    @NotBlank(message = "租赁人姓名不能为空")
    private String name;

    /**
     * 租赁人手机号
     */
    @Schema(description = "租赁人手机号", example = "13889890909")
    @NotBlank(message = "租赁人手机号不能为空")
    private String mobile;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型")
    private CertificateTypeDTO certificateType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码", example = "350824199809231111")
    @NotBlank(message = "证件号码不能为空")
    private String certificateNumber;

}
