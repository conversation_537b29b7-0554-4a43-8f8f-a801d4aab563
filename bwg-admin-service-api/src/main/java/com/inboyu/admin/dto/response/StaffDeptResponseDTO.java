package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025年08月05日 10:44
 */
@Data
public class StaffDeptResponseDTO {
    @Schema(description = "组织id")
    private String deptId;

    @Schema(description = "组织名称")
    private String title;

    @Schema(description = "是否包含所有门店")
    private Boolean includeAll;

    @Schema(description = "门店信息")
    private List<StaffStoreDTO> stores;

    @Schema(description = "角色信息")
    private List<StaffRoleDTO> roles;

    @Schema(description = "过期时间")
    private String expireTime;

}
