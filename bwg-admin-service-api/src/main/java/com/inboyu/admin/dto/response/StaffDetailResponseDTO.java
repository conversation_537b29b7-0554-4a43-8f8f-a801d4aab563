package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 10:41
 */
@Data
public class StaffDetailResponseDTO {
    @Schema(description = "员工id")
    private String staffId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "员工状态")
    private StaffStatusDTO status;

    @Schema(description = "组织信息")
    private List<StaffDeptResponseDTO> depts;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
