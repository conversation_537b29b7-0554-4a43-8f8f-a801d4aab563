package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 11:36
 */
@Data
@Builder
public class StoreDTO {
    @Schema(description = "门店id")
    private String storeId;

    @Schema(description = "门店名称")
    private String title;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "县（区）编码")
    private String countyCode;

    @Schema(description = "县（区）名称")
    private String countyName;

    @Schema(description = "详细地址")
    private String address;
}
