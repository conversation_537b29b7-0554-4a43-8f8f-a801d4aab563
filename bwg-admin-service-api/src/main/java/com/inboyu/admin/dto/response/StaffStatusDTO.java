package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月05日 10:31
 */
@Data
public class StaffStatusDTO {
    /**
     * 状态编码
     */
    @Schema(description = "状态编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态编码不能为空")
    private String code;
    /**
     * 角色状态描述
     */
    @Schema(description = "状态描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态描述不能为空")
    private String title;
}
