package com.inboyu.admin.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 坐标实体
 *
 * <AUTHOR> Dong
 * @date 2025年08月02日 11:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
public class GeoPointDTO {

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "纬度不能为空")
    private String latitude;
}
