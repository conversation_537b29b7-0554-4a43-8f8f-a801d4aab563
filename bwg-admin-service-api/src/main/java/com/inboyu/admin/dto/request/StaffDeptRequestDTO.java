package com.inboyu.admin.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 10:21
 */
@Data
public class StaffDeptRequestDTO {
    @Schema(description = "部门id")
    private String deptId;

    @Schema(description = "是否包含所有门店")
    private Boolean includeAll;

    @Schema(description = "门店id")
    private List<String> storeIds;

    @Schema(description = "角色id")
    private List<String> roleIds;

    @Schema(description = "过期时间")
    private String expireTime;
}
