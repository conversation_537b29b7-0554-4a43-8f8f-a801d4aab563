package com.inboyu.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年08月01日 16:32
 */
@Data
public class StaffPermissionDTO {
    @Schema(description = "权限编码")
    private String code;
    @Schema(description = "权限名称")
    private String title;
    @Schema(description = "接口URL")
    private String url;

    @Schema(description = "权限范围")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PermissionScopeDTO scope;
}
