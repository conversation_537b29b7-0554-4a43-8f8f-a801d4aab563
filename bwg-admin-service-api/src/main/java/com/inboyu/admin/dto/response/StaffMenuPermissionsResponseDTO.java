package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @date 2025年08月01日 16:25
 */
@Data
public class StaffMenuPermissionsResponseDTO {
    @Schema(description = "菜单编码")
    private String code;
    @Schema(description = "菜单名称")
    private String title;
    @Schema(description = "图标")
    private String icon;
    @Schema(description = "权限集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<StaffPermissionDTO> permissions;
    @Schema(description = "子菜单")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<StaffMenuPermissionsResponseDTO> children;
}
