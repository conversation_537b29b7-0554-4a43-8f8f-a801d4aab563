package com.inboyu.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerResponseDTO implements Serializable {

    /**
     * 租赁人id
     */
    @Schema(description = "租赁人id", example = "1")
    private Long customerId;

    /**
     * 租赁人姓名
     */
    @Schema(description = "租赁人姓名", example = "王五")
    private String name;

    /**
     * 租赁人手机号
     */
    @Schema(description = "租赁人手机号", example = "13889890909")
    private String mobile;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型", example = "certificate_type.Mainland_ID_card")
    private String certificateType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码", example = "387520200709145588")
    private String certificateNumber;
}