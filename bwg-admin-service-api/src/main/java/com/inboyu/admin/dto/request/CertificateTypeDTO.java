package com.inboyu.admin.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 证件类型
 */
@Data
public class CertificateTypeDTO {

    @Schema(description = "证件类型编码", example = "1")
    @NotBlank(message = "证件类型不能为空")
    private String code;

    @Schema(description = "证件类型名称", example = "身份证")
    private String title;
}
