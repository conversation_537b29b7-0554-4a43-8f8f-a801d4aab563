package com.inboyu.admin.api;

import com.inboyu.admin.constant.ApiInfo;
import com.inboyu.admin.dto.request.CustomerRequestDTO;
import com.inboyu.admin.dto.response.CustomerResponseDTO;
import com.inboyu.spring.cloud.starter.api.ApiClient;
import org.springframework.web.bind.annotation.*;

/**
 * 字典查询接口
 * 
 * <AUTHOR>
 */
@ApiClient(url = ApiInfo.API_URL)
public interface CustomerFeignClient {

    @PostMapping("/api/v1/customers")
    CustomerResponseDTO save(@RequestBody CustomerRequestDTO customerRequestDTO);

    @GetMapping("/api/v1/customers/list")
    CustomerResponseDTO get(@RequestParam(value = "keywords") String keywords);

    @GetMapping("/api/v1/customers")
    CustomerResponseDTO getCustomerByPhone(@RequestParam(value = "phone") String phone);

    @GetMapping("/api/v1/customers/{customerId}")
    CustomerResponseDTO getByCustomerId(@PathVariable(value = "customerId") String customerId);

}
