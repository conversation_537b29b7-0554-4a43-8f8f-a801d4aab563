package com.inboyu.admin.api;

import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import com.inboyu.admin.constant.ApiInfo;
import com.inboyu.admin.dto.response.StoreDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.spring.cloud.starter.api.ApiClient;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 门店Feign客户端接口
 *
 * <AUTHOR> Dong
 * @date 2025年08月06日
 */
@Component
@ApiClient(url = ApiInfo.API_URL)
public interface StoreFeignClient {
    /**
     * 条件检索门店
     *
     * @param roleScope 角色范围
     * @return {@link List<StoreDTO>}
     */
    @GetMapping("/api/v1/stores/filter")
    List<StoreDTO> filterStores(@RequestHeader("x-role-scope") String roleScope);

    /**
     * 获取门店详情
     * @param storeId
     * @return
     */
    @GetMapping("/api/v1/stores/{storeId}")
    public StoreDetailResponseDTO getStore(@Schema(description = "门店id") @PathVariable String storeId);

}