package com.inboyu.admin.api;

import com.inboyu.admin.constant.ApiInfo;
import com.inboyu.admin.dto.request.CustomerWxSaveDTO;
import com.inboyu.admin.dto.request.CustomerWxUpdateDTO;
import com.inboyu.admin.dto.response.CustomerWxResponseDTO;
import com.inboyu.spring.cloud.starter.api.ApiClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 字典查询接口
 * 
 * <AUTHOR>
 */
@ApiClient(url = ApiInfo.API_URL)
public interface CustomerWxFeignClient {

    @PostMapping("/api/v1/wx/customers/save")
    CustomerWxResponseDTO save(@RequestBody CustomerWxSaveDTO customerWxSaveDTO);

    @PostMapping("/api/v1/wx/customers/update")
    CustomerWxResponseDTO update(@RequestBody CustomerWxUpdateDTO customerWxUpdateDTO);

}
