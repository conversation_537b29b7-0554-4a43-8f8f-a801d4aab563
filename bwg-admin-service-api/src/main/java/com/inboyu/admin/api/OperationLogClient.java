package com.inboyu.admin.api;

import com.inboyu.admin.constant.ApiInfo;
import com.inboyu.admin.dto.request.OperationLogCollectRequestDTO;
import com.inboyu.admin.dto.response.OperationLogCollectResponseDTO;
import com.inboyu.spring.cloud.starter.api.ApiClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 操作日志api接口
 * <AUTHOR>
 */
@ApiClient(url = ApiInfo.API_URL)
public interface OperationLogClient {

    @Operation(summary = "操作日志采集")
    @PostMapping("/api/v1/operation-log")
    OperationLogCollectResponseDTO collect(@Parameter(description = "操作日志采集请求实体",required = true) @RequestBody OperationLogCollectRequestDTO operationLogCollectRequestDTO);
}
