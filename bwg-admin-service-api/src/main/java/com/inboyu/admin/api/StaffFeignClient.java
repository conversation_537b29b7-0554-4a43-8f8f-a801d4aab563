package com.inboyu.admin.api;

import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.inboyu.admin.constant.ApiInfo;
import com.inboyu.admin.dto.response.StaffDetailResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuResponseDTO;
import com.inboyu.spring.cloud.starter.api.ApiClient;

/**
 * 员工api接口
 *
 * <AUTHOR> Dong
 * @date 2025年08月01日 16:13
 */
@Component
@ApiClient(url = ApiInfo.API_URL)
public interface StaffFeignClient {
    /**
     * 获取员工菜单权限
     *
     * @param staffId
     * @return {@link StaffMenuResponseDTO }
     * <AUTHOR>
     * @date 2025/08/01 16:37:15
     */
    @GetMapping("/api/v1/staffs/{staffId}/permissions")
    StaffMenuResponseDTO getStaffPermissions(@PathVariable String staffId);

    @GetMapping("/api/v1/staffs/permissions")
    StaffMenuResponseDTO getAdminPermissions();

    @GetMapping("/api/v1/staffs/{staffId}")
    StaffDetailResponseDTO getStaffDetail(@PathVariable String staffId);
}
