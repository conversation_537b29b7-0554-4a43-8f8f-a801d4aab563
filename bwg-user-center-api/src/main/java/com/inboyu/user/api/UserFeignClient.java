package com.inboyu.user.api;

import com.inboyu.spring.cloud.starter.api.ApiClient;
import com.inboyu.user.dto.UserResponse;

import org.springframework.web.bind.annotation.*;

/**
 * 用户中心服务间接口依赖
 * <AUTHOR>
 */
@ApiClient(url = "http://user-center-app")
public interface UserFeignClient {

    /**
     * 根据用户标识获取用户
     * @param userId 用户标识
     * @return R
     */
    @GetMapping("/api/v1/users/{userId}")
    UserResponse getUserByUserId(@PathVariable("userId") Long userId);

}
