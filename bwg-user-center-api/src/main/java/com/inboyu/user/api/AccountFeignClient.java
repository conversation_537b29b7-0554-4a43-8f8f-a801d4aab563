package com.inboyu.user.api;

import com.inboyu.spring.cloud.starter.api.ApiClient;
import com.inboyu.user.dto.CreatePhoneAccountRequest;
import com.inboyu.user.dto.UserResponse;

import org.springframework.web.bind.annotation.*;

/**
 * 用户中心服务间接口依赖
 * <AUTHOR>
 */
@ApiClient(url = "http://user-center-app")
public interface AccountFeignClient {

    /**
     * 根据手机号获取用户
     * @param phone 手机号
     * @return R
     */
    @GetMapping("/api/v1/accounts")
    UserResponse getUserByPhone(@RequestParam("phone") String phone);

    /**
     * 根据手机号创建用户
     * @param request 请求
     * @return R
     */
    @PostMapping("/api/v1/accounts")
    UserResponse createUserByPhone(@RequestBody CreatePhoneAccountRequest request);

}
