package com.inboyu.user.dto;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 用户信息
 * <AUTHOR>
 */
@Data
public class UserResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录手机号（脱敏处理）
     */
    private String phone;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

}
