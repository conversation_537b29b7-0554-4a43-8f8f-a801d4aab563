<?xml version="1.0"?>
<project
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.inboyu</groupId>
        <artifactId>iby-starter-dependencies</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <artifactId>iby-spring-cloud-starter-web</artifactId>
    <name>iby-spring-cloud-starter-web</name>
    <url>http://maven.apache.org</url>
    <properties>
        <main.basedir>${basedir}/../..</main.basedir>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <ahas-sentinel-client.version>1.8.0</ahas-sentinel-client.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>iby-spring-cloud-starter-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.inboyu</groupId>
            <artifactId>iby-spring-cloud-starter-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
    </dependencies>
</project>
