package com.inboyu.user.infrastructure.helper;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.function.Function;

/**
 * 字段引用工具
 * <AUTHOR>
 */
@FunctionalInterface
public interface FieldRef<T, R> extends Function<T, R>, Serializable {
    default String getFieldName() {
        try {
            Method writeReplace = this.getClass().getDeclaredMethod("writeReplace");
            writeReplace.setAccessible(true);
            SerializedLambda serializedLambda = (SerializedLambda) writeReplace.invoke(this);

            String methodName = serializedLambda.getImplMethodName();
            if (methodName.startsWith("get")) {
                return methodName.substring(3, 4).toLowerCase() + methodName.substring(4);
            } else if (methodName.startsWith("is")) {
                return methodName.substring(2, 3).toLowerCase() + methodName.substring(3);
            }

            throw new IllegalArgumentException("Method is not a getter: " + methodName);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (NoSuchMethodException | IllegalAccessException e) {
            throw new RuntimeException("Failed to resolve field name", e);
        }
    }
}