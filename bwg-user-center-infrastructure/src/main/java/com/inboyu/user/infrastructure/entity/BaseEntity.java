package com.inboyu.user.infrastructure.entity;

import com.inboyu.user.infrastructure.helper.FieldRef;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 表基础结构
 * <AUTHOR>
 */
@Data
@MappedSuperclass
@DynamicInsert
@DynamicUpdate
public abstract class BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="create_time")
    private LocalDateTime createTime;

    @Column(name="modify_time")
    private LocalDateTime modifyTime;

    private Boolean deleted = false;

    @Version
    private Integer version;

    @PrePersist
    protected void onCreate() {
        this.createTime = LocalDateTime.now();
        this.modifyTime = this.createTime;
    }

    @PreUpdate
    protected void onUpdate() {
        this.modifyTime = LocalDateTime.now();
    }

    // 静态工厂方法
    protected static <T, R> FieldRef<T, R> field(FieldRef<T, R> ref) {
        return ref;
    }
}
