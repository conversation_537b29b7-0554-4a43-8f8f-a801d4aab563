package com.inboyu.user.infrastructure.entity;

import com.inboyu.spring.cloud.starter.jpa.annotation.Encrypted;
import com.inboyu.spring.cloud.starter.jpa.converter.SensitiveConverter;
import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.AccountId;
import com.inboyu.user.domain.model.PhoneNumber;
import com.inboyu.user.domain.model.UserId;
import com.inboyu.user.infrastructure.helper.FieldRef;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

/**
 * 账号表
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_account")
public class AccountEntity extends BaseEntity {

    /**
     * 账号ID
     */
    @Column(name = "account_id")
    private Long accountId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 账号类型
     */
    private String type;

    /**
     * 账号标识
     */
    @Encrypted
    @Convert(converter = SensitiveConverter.class)
    private String identifier;

    public static final FieldRef<AccountEntity, String> TYPE = field(AccountEntity::getType);
    public static final FieldRef<AccountEntity, String> IDENTIFIER = field(AccountEntity::getIdentifier);
    public static final FieldRef<AccountEntity, Long> USER_ID = field(AccountEntity::getUserId);

    public static AccountEntity from(Account account) {
        AccountEntity entity = new AccountEntity();
        entity.setAccountId(account.getAccountId().getValue());
        entity.setUserId(account.getUserId().getValue());
        entity.setType(account.getType().getCode());
        entity.setIdentifier(account.getIdentifier());
        return entity;
    }

    /**
     * 转成账号实体
     * 
     * @return 账号实体
     */
    @Transient
    public Optional<Account> toPhoneAccount() {
        UserId userId = UserId.of(this.userId);
        AccountId accountId = AccountId.of(this.accountId);
        PhoneNumber phoneNumber = PhoneNumber.of(this.identifier);
        Account account = Account.create(userId, accountId, phoneNumber);
        return Optional.of(account);
    }

    public void merge(Account account) {
        this.type = account.getType().getCode();
        this.identifier = account.getIdentifier();
    }
}
