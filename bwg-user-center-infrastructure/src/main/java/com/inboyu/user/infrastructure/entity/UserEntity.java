package com.inboyu.user.infrastructure.entity;

import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.User;
import com.inboyu.user.domain.model.UserId;
import com.inboyu.user.infrastructure.helper.FieldRef;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

/**
 * 用户表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_user")
public class UserEntity extends  BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    public static final FieldRef<UserEntity, Long> USER_ID = field(UserEntity::getUserId);

    public static UserEntity from(User user) {
        UserEntity entity = new UserEntity();
        entity.setUserId(user.getUserId().getValue());
        entity.setNickname(user.getNickname());
        entity.setAvatar(user.getAvatar());
        return entity;
    }

    public void merge(User user) {
        this.nickname = user.getNickname();
        this.avatar = user.getAvatar();
    }

    public Optional<User> toUser(Account account) {
        UserId uid = UserId.of(this.userId);
        User user = User.of(uid, this.nickname, this.avatar);
        user.addPhoneAccount(account);
        return Optional.of(user);
    }

}
