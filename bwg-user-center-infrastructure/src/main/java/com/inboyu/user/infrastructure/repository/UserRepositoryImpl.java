package com.inboyu.user.infrastructure.repository;

import com.inboyu.spring.cloud.starter.redis.utils.RedisUtil;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.User;
import com.inboyu.user.domain.model.UserId;
import com.inboyu.user.domain.repository.AccountRepository;
import com.inboyu.user.domain.repository.UserRepository;
import com.inboyu.user.infrastructure.entity.UserEntity;
import com.inboyu.user.infrastructure.helper.JpaUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * 账号数仓实现
 * <AUTHOR>
 */
@Repository
public class UserRepositoryImpl implements UserRepository {
    private static final long CACHE_DELAY_TIME = 500;
    private static final String USER_ACCOUNT_KEY = "cache:%s";
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Optional<UserId> generateId() {
        return Optional.of(UserId.of(snowflakeIdGenerator.nextId()));
    }

    @Override
    public Optional<User> getAccountUser(Account account) {
        if (account == null) {
            throw new IllegalArgumentException("account must not be empty");
        }
        //查缓存
        if (redisUtil.hasKey(String.format(USER_ACCOUNT_KEY, account.getUserId().getValue()))) {
            UserEntity entity = redisUtil.get(String.format(USER_ACCOUNT_KEY, account.getUserId().getValue()));
            return entity.toUser(account);
        }
        //查数据库
        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<UserEntity> query = cb.createQuery(UserEntity.class);
        Root<UserEntity> root = query.from(UserEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, UserEntity.USER_ID), account.getUserId().getValue()));

        try {
            UserEntity entity = entityManager.createQuery(query).getSingleResult();
            //加入缓存
            redisUtil.set(String.format(USER_ACCOUNT_KEY, account.getUserId().getValue()), entity);
            return entity.toUser(account);
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    @Override
    public void save(User user) {
        //删缓存
        redisUtil.del(String.format(USER_ACCOUNT_KEY, user.getUserId().getValue()));
        if (user == null) {
            throw new IllegalArgumentException("Account cannot be null");
        }

        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<UserEntity> query = cb.createQuery(UserEntity.class);
        Root<UserEntity> root = query.from(UserEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, UserEntity.USER_ID), user.getUserId().getValue()));

        try {
            UserEntity entity = entityManager.createQuery(query).getSingleResult();
            entity.merge(user);
            entityManager.merge(entity);
        } catch (NoResultException e) {
            UserEntity entity = UserEntity.from(user);
            entityManager.persist(entity);
        }
        //延迟500ms再删
        new Thread(() -> {
            try {
                Thread.sleep(CACHE_DELAY_TIME);
                // 删除缓存
                redisUtil.del(String.format(USER_ACCOUNT_KEY, user.getUserId().getValue()));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    @Override
    public Optional<User> get(UserId userId) {
        if (userId == null) {
            throw new IllegalArgumentException("Account cannot be null");
        }
        //查缓存
        if (redisUtil.hasKey(String.format(USER_ACCOUNT_KEY, userId.getValue()))) {
            UserEntity entity = redisUtil.get(String.format(USER_ACCOUNT_KEY, userId.getValue()));
            return entity.toUser(accountRepository.getAccountByUserId(userId).get());
        }
        //查数据库
        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<UserEntity> query = cb.createQuery(UserEntity.class);
        Root<UserEntity> root = query.from(UserEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, UserEntity.USER_ID), userId.getValue()));

        try {
            Optional<UserEntity> entity = Optional.of(entityManager.createQuery(query).getSingleResult());
            Optional<Account> account = accountRepository.getAccountByUserId(userId);
            //加入缓存
            redisUtil.set(String.format(USER_ACCOUNT_KEY, userId.getValue()), entity.get());
            return entity.get().toUser(account.get());
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

}
