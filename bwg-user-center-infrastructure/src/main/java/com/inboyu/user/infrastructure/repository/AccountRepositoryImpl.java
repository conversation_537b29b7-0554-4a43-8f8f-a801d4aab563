package com.inboyu.user.infrastructure.repository;

import com.inboyu.spring.cloud.starter.redis.utils.RedisUtil;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.AccountId;
import com.inboyu.user.domain.model.AccountType;
import com.inboyu.user.domain.model.UserId;
import com.inboyu.user.domain.repository.AccountRepository;
import com.inboyu.user.infrastructure.entity.AccountEntity;
import com.inboyu.user.infrastructure.helper.JpaUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 账号数仓实现
 * <AUTHOR>
 */
@Repository
public class AccountRepositoryImpl implements AccountRepository {
    private static final long CACHE_DELAY_TIME = 500;

    private static final String ACCOUNT_USER_KEY = "cache_%s_%s";

    private static final String Account_UsrId_KEY = "cache_%s";
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private RedisUtil redisUtil; 

    @Override
    public Optional<AccountId> generateId() {
        return Optional.of(AccountId.of(snowflakeIdGenerator.nextId()));
    }

    @Override
    public Optional<Account> get(AccountType type, String identifier) {
        // 查缓存
        if (redisUtil.hasKey(String.format(ACCOUNT_USER_KEY, type.getCode(), identifier))) {
            AccountEntity entity = redisUtil.get(String.format(ACCOUNT_USER_KEY, type.getCode(), identifier));
            return entity.toPhoneAccount();
        }
        // 查数据库
        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<AccountEntity> query = cb.createQuery(AccountEntity.class);
        Root<AccountEntity> root = query.from(AccountEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, AccountEntity.TYPE), type.getCode()),
                        cb.equal(JpaUtils.getPath(root, AccountEntity.IDENTIFIER), identifier));
        try {
            Optional<AccountEntity> entity = Optional.of(entityManager.createQuery(query).getSingleResult());
            AccountEntity e = entity.get();
            // account 加入缓存
            redisUtil.set(String.format(ACCOUNT_USER_KEY, type.getCode(), identifier), e, 7200);
            return e.toPhoneAccount();
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    @Override
    public void save(Account account) {
        //删缓存
        redisUtil.del(String.format(ACCOUNT_USER_KEY, account.getType().getCode(), account.getIdentifier()));
        if (account == null) {
            throw new IllegalArgumentException("Account cannot be null");
        }

        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<AccountEntity> query = cb.createQuery(AccountEntity.class);
        Root<AccountEntity> root = query.from(AccountEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, AccountEntity.TYPE), account.getType().getCode()),
                        cb.equal(JpaUtils.getPath(root, AccountEntity.IDENTIFIER), account.getIdentifier()));

       try {
           AccountEntity entity = entityManager.createQuery(query).getSingleResult();
           entity.merge(account);
           entityManager.merge(entity);
       } catch (NoResultException e) {
           AccountEntity entity = AccountEntity.from(account);
           entityManager.persist(entity);
       }
       //延迟500ms再删
        new Thread(() -> {
            try {
                Thread.sleep(CACHE_DELAY_TIME);
                redisUtil.del(String.format(ACCOUNT_USER_KEY, account.getType().getCode(), account.getIdentifier()));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 推荐写法
            }
        }).start();

    }

    @Override
    public Optional<Account> getAccountByUserId(UserId userId) {
        //查缓存
        if (redisUtil.hasKey(String.format(Account_UsrId_KEY, userId.getValue()))) {
            AccountEntity entity = redisUtil.get(String.format(Account_UsrId_KEY, userId.getValue()));
            return entity.toPhoneAccount();
        }
        //查数据库
        CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
        CriteriaQuery<AccountEntity> query = cb.createQuery(AccountEntity.class);
        Root<AccountEntity> root = query.from(AccountEntity.class);
        // 使用类型安全的字段引用
        query.select(root)
                .where(cb.equal(JpaUtils.getPath(root, AccountEntity.USER_ID), userId.getValue()));
        try {
            Optional<AccountEntity> entity = Optional.of(entityManager.createQuery(query).getSingleResult());
            redisUtil.set(String.format(Account_UsrId_KEY, userId.getValue()), entity.get(), 7200);
            return entity.get().toPhoneAccount();
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

}
