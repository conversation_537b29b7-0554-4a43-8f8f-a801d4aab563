<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.inboyu</groupId>
		<artifactId>iby-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<artifactId>bwg-sales-center-infrastructure</artifactId>
	<version>${bwg.sales.center.version}</version>

	<dependencies>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-http</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-sales-center-domain</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-sales-center-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-alimq</artifactId>
		</dependency>
	</dependencies>

</project>
