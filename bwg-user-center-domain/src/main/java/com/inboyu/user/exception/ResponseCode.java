package com.inboyu.user.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年07月29日 20:07
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 用户中心错误码范围 17000 ～ 17999*/
    SUCCESS(0, "成功"),
    USER_NOT_EXIST(17001, "用户不存在"),
    USER_EXIST(17002, "用户已存在"),;
    /** 用户中心错误码范围 17000 ～ 17999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
