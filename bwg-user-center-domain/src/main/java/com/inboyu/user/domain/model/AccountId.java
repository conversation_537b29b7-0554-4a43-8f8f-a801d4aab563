package com.inboyu.user.domain.model;

import lombok.Getter;

/**
 * 账号ID 值对象
 * <AUTHOR>
 */
@Getter
public final class AccountId {

    private final Long value;

    private AccountId(Long value) {
        this.value = value;
    }

    public static AccountId of(Long id) {
        return new AccountId(id);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }
}
