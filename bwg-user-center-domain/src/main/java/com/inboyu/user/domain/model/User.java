package com.inboyu.user.domain.model;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;

/**
 * 用户 聚合根
 * <AUTHOR>
 */
@Getter
public class User {

    /**
     * 用户 ID
     */
    private final UserId userId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 账号
     */
    private List<AccountId> accountIds = new ArrayList<>();

    private User(UserId userId) {
        this.userId = userId;
    }

    public static User create(UserId userId) {
        User user = new User(userId);
        user.nickname = "";
        user.avatar = "";
        return user;
    }

    public static User of(UserId userId, String nickname, String avatar) {
        User user = new User(userId);
        user.nickname = nickname;
        user.avatar = avatar;
        return user;
    }

    public void addPhoneAccount(Account account) {
        this.accountIds.add(account.getAccountId());
    }
}
