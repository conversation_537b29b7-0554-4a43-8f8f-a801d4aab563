package com.inboyu.user.domain.repository;

import java.util.Optional;

import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.User;
import com.inboyu.user.domain.model.UserId;

/**
 * 用户数仓
 * <AUTHOR>
 */
public interface UserRepository {

    /**
     * 生成用户Id
     * @return
     */
    Optional<UserId> generateId();

    /**
     * 根据账号查询用户
     * @param account 账号
     * @return 用户信息
     */
    Optional<User> getAccountUser(Account account);

    /**
     * 根据用户Id查询用户
     * @param userId 用户Id
     * @return 用户信息
     */
    Optional<User> get(UserId usrId);

    /**
     * 保存用户
     * @param user 账号
     */
    void save(User user);
    
}
