package com.inboyu.user.domain.model;

import lombok.Getter;

/**
 * 手机号值对象
 * 
 * <AUTHOR>
 */
@Getter
public final class PhoneNumber {

    private final String value;

    private PhoneNumber(String phoneNumber) {
        this.value = phoneNumber;
    }

    public static PhoneNumber of(String phoneNumber) {
        validate(phoneNumber);
        return new PhoneNumber(phoneNumber);
    }

    public static void validate(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            throw new IllegalArgumentException("手机号不能为空");
        }
        // 验证手机号格式：以1开头，第二位是3-9之间的数字，后面跟9位数字
        String regex = "^1[3-9]\\d{9}$";
        if (!phoneNumber.matches(regex)) {
            throw new IllegalArgumentException("无效的手机号格式");
        }
    }

    /**
     * 隐藏手机号中间四位
     * @return
     */
    public String format() {
        return this.value.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1 **** $2");
    }
}
