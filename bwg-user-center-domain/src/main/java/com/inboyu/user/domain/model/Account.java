package com.inboyu.user.domain.model;

import lombok.Data;

/**
 * 账号 实体
 * <AUTHOR>
 */
@Data
public class Account {

    /**
     * 账号ID
     */
    private final AccountId accountId;

    /**
     * 用户ID
     */
    private final UserId userId;

    /**
     * 账号类型
     */
    private AccountType type;

    /**
     * 账号标识
     */
    private String identifier;

    /**
     * 新增账号
     * @param userId 用户ID
     */
    private Account(UserId userId, AccountId accountId) {
        this.accountId = accountId;
        this.userId = userId;
    }

    /**
     * 静态工厂创建
     * @param userId 用户ID
     * @param phoneNumber 手机号
     * @return 手机账号
     */
    public static Account create(UserId userId, AccountId accountId, PhoneNumber phoneNumber) {
        Account account =  new Account(userId, accountId);
        account.type = AccountType.PHONE_NUMBER;
        account.identifier = phoneNumber.getValue();
        return account;
    }

}
