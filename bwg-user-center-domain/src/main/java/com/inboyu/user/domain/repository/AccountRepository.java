package com.inboyu.user.domain.repository;

import java.util.Optional;

import com.inboyu.user.domain.model.Account;
import com.inboyu.user.domain.model.AccountId;
import com.inboyu.user.domain.model.AccountType;
import com.inboyu.user.domain.model.PhoneNumber;
import com.inboyu.user.domain.model.UserId;

/**
 * 账号数仓
 * <AUTHOR>
 */
public interface AccountRepository {


    /**
     * 生成账号ID
     * @return
     */
    Optional<AccountId> generateId();

    /**
     * 通过类型获取账号
     * @param type 账号类型
     * @param identifier 账号标识
     * @return 账号
     */
    Optional<Account> get(AccountType type, String identifier);

    default Optional<Account> getPhoneAccount(PhoneNumber phoneNumber) {
        return this.get(AccountType.PHONE_NUMBER, phoneNumber.getValue());
    }

    /**
     * 保存账号
     * @param account 账号
     */
    void save(Account account);

    /**
     * 根据用户id获取账号
     * @param userId 用户id
     * @return 账号
     */
    Optional<Account> getAccountByUserId(UserId userId);
}
