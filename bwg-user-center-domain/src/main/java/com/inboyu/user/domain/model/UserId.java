package com.inboyu.user.domain.model;

import lombok.Data;

/**
 * 用户标识 值对象
 * <AUTHOR>
 */
@Data
public final class UserId {

    private final Long value;

    private UserId(Long value) {
        this.value = value;
    }

    public static UserId of(Long userId) {
        return new UserId(userId);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }
}
