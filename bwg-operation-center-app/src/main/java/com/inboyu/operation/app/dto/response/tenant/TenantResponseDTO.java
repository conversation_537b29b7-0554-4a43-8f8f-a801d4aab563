package com.inboyu.operation.app.dto.response.tenant;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "运营商信息")
public class TenantResponseDTO {

    /**
     * 运营商ID
     */
    @Schema(description = "运营商ID", example = "341065504682807296")
    private String tenantId;

    /**
     * 运营商名称
     */
    @Schema(description = "运营商名称", example = "示例科技有限公司")
    private String title;

    /**
     * 超管账号信息
     */
    @Schema(description = "超管账号信息")
    private TenantResponseDTO.Admin admin;

    /**
     * 证件信息
     */
    @Schema(description = "证件信息")
    private TenantResponseDTO.License license;

    /**
     * 联系人信息
     */
    @Schema(description = "联系人信息")
    private Contact contact;

    /**
     * 运营商审核状态
     */
    @Schema(description = "审核状态")
    private Status status;

    /**
     * 备注
     */
    @Schema(description = "备注信息", example = "优质客户")
    private String remark;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间", example = "2025-08-01 10:10:49")
    private String createTime;

    /**
     * 超管账号信息子对象
     */
    @Data
    @Schema(description = "超管账号信息")
    public static class Admin {
        /**
         * 管理员ID
         */
        @Schema(description = "管理员ID")
        private String userId;

        /**
         * 登录手机号
         */
        @Schema(description = "登录手机号，脱敏处理", example = "13800138000")
        private String phone;

        /**
         * 登录手机号对应的姓名
         */
        @Schema(description = "登录手机号对应的姓名", example = "张三")
        private String name;
    }

    /**
     * 证件信息子对象
     */
    @Data
    @Schema(description = "证件信息")
    public static class License {
        /**
         * 证件类型
         */
        @Schema(description = "证件类型")
        private LicenseType type;

        /**
         * 证件号码
         */
        @Schema(description = "证件号码", example = "110101199001011234")
        private String identifier;
    }

    /**
     * 证件信息类型子对象
     */
    @Data
    @Schema(description = "证件类型")
    public static class LicenseType {
        /**
         * 证件类型
         */
        @Schema(description = "证件类型-字典表维护", example = "license_type.id_card_no")
        private String code;

        @Schema(description = "证件类型", example = "身份证")
        private String title;
    }

    /**
     * 联系人信息子对象
     */
    @Data
    @Schema(description = "联系人信息")
    public static class Contact {
        /**
         * 联系人姓名
         */
        @Schema(description = "联系人姓名", example = "张三")
        private String name;

        /**
         * 联系人手机号
         */
        @Schema(description = "联系人手机号", example = "13900139000")
        private String phone;
    }

    /**
     * 运营商状态子对象
     */
    @Data
    @Schema(description = "运营商审核状态")
    public static class Status {
        /**
         * 证件类型
         */
        @Schema(description = "状态字典编码", example = "tenant_status.activated")
        private String code;

        /**
         * 证件号码
         */
        @Schema(description = "状态Title", example = "待审核")
        private String title;
    }
}
