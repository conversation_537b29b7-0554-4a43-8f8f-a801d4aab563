package com.inboyu.operation.app.controller;

import com.inboyu.operation.app.application.TenantAppService;
import com.inboyu.operation.app.dto.request.tenant.TenantRequestDTO;
import com.inboyu.operation.app.dto.request.tenant.TenantServiceRequestDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantListResponseDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/tenants")
@Tag(name = "运营商管理")
public class TenantController {

    @Autowired
    private TenantAppService tenantAppService;

    /**
     * @return
     * @see <a href="https://inboyu.yuque.com/vo9bfe/project_doc/bq2wb6gyx8kh28uw#djqfO">...</a>
     */
    @PostMapping
    @Operation(summary = "新增运营商")
    public TenantResponseDTO addTenant(@RequestBody @Validated TenantRequestDTO tenantRequestDTO) {
        return tenantAppService.createTenant(tenantRequestDTO);
    }

    /**
     * @return
     *
     */
    @PostMapping("{tenantId}")
    @Operation(summary = "编辑运营商")
    public TenantResponseDTO editTenant(
            @RequestBody @Validated TenantRequestDTO tenantRequestDTO,
            @Schema(description = "运营商ID", example = "341518448242855936") @PathVariable(value = "tenantId") Long tenantId
    ) {
        return tenantAppService.editTenant(tenantRequestDTO, tenantId);
    }

    /**
     *
     * @return
     */
    @GetMapping("{tenantId}")
    @Operation(summary = "获取指定运营商信息")
    public TenantResponseDTO getTenant(@Schema(description = "运营商ID", example = "341518448242855936") @PathVariable(value = "tenantId") Long tenantId) {

        return tenantAppService.queryTenantInfo(tenantId);
    }

    /**
     * @return
     * @see <a href="https://inboyu.yuque.com/vo9bfe/project_doc/bq2wb6gyx8kh28uw#qx5vi">...</a>
     */
    @GetMapping
    @Operation(summary = "获取运营商列表")
    public TenantListResponseDTO getTenantList(
            @Schema(description = "当前查询页码", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @Schema(description = "每页展示行数", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            @Schema(description = "搜索关键字，名称、证件号、登录手机，可模糊搜索运营商") @RequestParam(value = "keywords", defaultValue = "") String keywords
    ) {
        return tenantAppService.getTenantList(pageNum, pageSize, keywords);
    }

    @PostMapping("{tenantId}/service")
    @Operation(summary = "开通运营商服务")
    public void changServiceStatus(
            @RequestBody @Validated TenantServiceRequestDTO tenantServiceRequestDTO,
            @Schema(description = "运营商ID", example = "341518448242855936") @PathVariable(value = "tenantId") Long tenantId
    ) {
        tenantAppService.changeServiceStatus(tenantId, tenantServiceRequestDTO);
    }
}

