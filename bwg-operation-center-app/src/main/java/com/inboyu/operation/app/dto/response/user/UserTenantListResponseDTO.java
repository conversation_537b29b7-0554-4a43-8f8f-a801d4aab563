package com.inboyu.operation.app.dto.response.user;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 运营商列表DTO
 * 用于封装包含多个运营商基本信息的列表数据
 */
@Data
@Schema(description = "运营商列表数据")
public class UserTenantListResponseDTO {
    /**
     * 运营商列表
     */
    @Schema(description = "运营商信息列表", example = "[{\"tenantId\":123456,\"title\":\"示例运营商\"}]")
    private List<TenantSimpleDTO> tenants;

    /**
     * 运营商基本信息DTO
     * 包含运营商ID和名称的简化信息
     */
    @Data
    @Schema(description = "运营商基本信息（ID和名称）")
    public static class TenantSimpleDTO {

        /**
         * 运营商ID
         */
        @Schema(description = "运营商唯一标识（雪花算法生成）", example = "1234567890123456789")
        private String tenantId;

        /**
         * 运营商名称
         */
        @Schema(description = "运营商的名称", example = "云服务科技有限公司")
        private String title;

        /**
         * user_id  对应的  员工ID
         */
        @Schema(description = "运营商的名称", example = "2736515728288")
        private String staffId;

        @Schema(description = "是否是超管用户", example = "true")
        private Boolean isAdmin;

        @Schema(description = "超管用户姓名，只有isAdmin=true才有值", example = "李四")
        private String adminName;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", example = "true")
        private Boolean staffStatus;
    }
}
