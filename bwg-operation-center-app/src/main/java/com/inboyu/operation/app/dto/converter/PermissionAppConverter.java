package com.inboyu.operation.app.dto.converter;

import com.inboyu.operation.app.dto.response.permission.MenuPermissionResponseDTO;
import com.inboyu.operation.domain.permission.model.PermissionGroup;
import com.inboyu.operation.domain.permission.model.PermissionScope;
import com.inboyu.util.convert.PojoConvertUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;


@Component
public class PermissionAppConverter {

    public MenuPermissionResponseDTO.MenuDTO toMenuTDO(PermissionGroup permissionGroup) {
        MenuPermissionResponseDTO.MenuDTO menuDTO = PojoConvertUtil.convert(permissionGroup, MenuPermissionResponseDTO.MenuDTO.class);
        menuDTO.setCode(permissionGroup.getCode().getValue());

        return menuDTO;
    }

    public MenuPermissionResponseDTO.PermissionDTO toPermissionDTO(PermissionScope permissionScope) {
        MenuPermissionResponseDTO.PermissionDTO permissionDTO = PojoConvertUtil.convert(permissionScope, MenuPermissionResponseDTO.PermissionDTO.class);
        permissionDTO.setCode(permissionScope.getPermission().getValue());

        return permissionDTO;
    }

    /**
     * 转换为菜单权限树
     *
     * @param permissionGroupList 菜单定义列表
     * @param permissionScopeList 权限定义列表
     * @return
     */
    public MenuPermissionResponseDTO toMenuPermissionResponseDTO(
            List<PermissionGroup> permissionGroupList,
            List<PermissionScope> permissionScopeList
    ) {

        //转换 permissionScopeList 为 MenuPermissionResponseDTO.PermissionDTO
        // 并按照 permissionScopeList[].group_code Hash 分组
        HashMap<String, List<MenuPermissionResponseDTO.PermissionDTO>> groupCodeToPermissionMap = new HashMap<>();
        for (PermissionScope permissionScope : permissionScopeList) {
            if (!groupCodeToPermissionMap.containsKey(permissionScope.getGroupCode().getValue())) {
                groupCodeToPermissionMap.put(permissionScope.getGroupCode().getValue(), new ArrayList<MenuPermissionResponseDTO.PermissionDTO>());
            }
            groupCodeToPermissionMap.get(permissionScope.getGroupCode().getValue())
                    .add(toPermissionDTO(permissionScope));
        }

        //permissionGroupList 转换为 MenuPermissionResponseDTO.MenuDTO
        HashMap<String, List<MenuPermissionResponseDTO.MenuDTO>> groupCodeToMenuMap = new HashMap<>();
        for (PermissionGroup permissionGroup : permissionGroupList) {
            if (!groupCodeToMenuMap.containsKey(permissionGroup.getGroupCode().getValue())) {
                groupCodeToMenuMap.put(permissionGroup.getGroupCode().getValue(), new ArrayList<>());
            }

            MenuPermissionResponseDTO.MenuDTO menuDTO = toMenuTDO(permissionGroup);
            List<MenuPermissionResponseDTO.PermissionDTO> permissionDTOList = groupCodeToPermissionMap.get(permissionGroup.getCode().getValue());
            if (permissionDTOList == null) {
                permissionDTOList = Collections.emptyList();
            }
            menuDTO.setPermissions(permissionDTOList);

            groupCodeToMenuMap.get(permissionGroup.getGroupCode().getValue())
                    .add(menuDTO);
        }

        //并生成数 结构
        String code = "";//根节点默认编码是空字符串
        List<MenuPermissionResponseDTO.MenuDTO> menuDTOList = findMenuChildren(code, groupCodeToMenuMap);

        MenuPermissionResponseDTO menuPermissionResponseDTO = new MenuPermissionResponseDTO();
        menuPermissionResponseDTO.setMenus(menuDTOList);

        return menuPermissionResponseDTO;
    }

    /**
     * 查找子菜单数据
     * @param code 当前菜单的编码
     * @param groupCodeToMenuMap 以 groupCode 为 key分组的菜单MAP数据
     * @return 当前菜单的子菜单列表
     */
    protected List<MenuPermissionResponseDTO.MenuDTO> findMenuChildren(
            String code,
            HashMap<String, List<MenuPermissionResponseDTO.MenuDTO>> groupCodeToMenuMap
            ) {

        List<MenuPermissionResponseDTO.MenuDTO> childrenList = groupCodeToMenuMap.get(code);

        if (childrenList == null) {
            return Collections.emptyList();
        }

        for (MenuPermissionResponseDTO.MenuDTO menuDTO : childrenList) {
            menuDTO.setChildren(findMenuChildren(menuDTO.getCode(), groupCodeToMenuMap));
        }

        return childrenList;
    }

}
