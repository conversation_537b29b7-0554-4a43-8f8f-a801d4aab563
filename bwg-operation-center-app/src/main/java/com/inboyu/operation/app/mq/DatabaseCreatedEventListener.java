package com.inboyu.operation.app.mq;

import com.inboyu.alimq.annotation.RocketMQTag;
import com.inboyu.alimq.annotation.RocketMQTopic;
import com.inboyu.operation.app.application.TenantAppService;
import com.inboyu.operation.constant.OperationRocketMqTopicConstant;
//import com.inboyu.osat.event.DatabaseCreatedEvent;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 运营商开通/关闭事件
 */
@RocketMQTopic(topic = OperationRocketMqTopicConstant.TOPIC_BWG_TENANT)
public class DatabaseCreatedEventListener {

    @Autowired
    TenantAppService tenantAppService;
//
//    /**
//     * 运营商员数据库初始化完成
//     * @param message
//     */
//    @RocketMQTag(tag = OperationRocketMqTopicConstant.TAG_DATABASE_CREATED)
//    public void databaseCreate(DatabaseCreatedEvent message) {
//        tenantAppService.databaseCreatedCompleted(message);
//    }
}
