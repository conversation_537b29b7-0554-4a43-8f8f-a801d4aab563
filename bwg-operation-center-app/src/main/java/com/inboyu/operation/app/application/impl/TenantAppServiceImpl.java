package com.inboyu.operation.app.application.impl;

import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.operation.app.application.TenantAppService;
import com.inboyu.operation.app.dto.converter.TenantAppConverter;
import com.inboyu.operation.app.dto.request.tenant.TenantRequestDTO;
import com.inboyu.operation.app.dto.request.tenant.TenantServiceRequestDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantListResponseDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantResponseDTO;
import com.inboyu.operation.app.dto.response.user.UserTenantListResponseDTO;
import com.inboyu.operation.domain.tenant.model.*;
import com.inboyu.operation.domain.tenant.service.TenantDomainService;
import com.inboyu.operation.exception.ResponseCode;
//import com.inboyu.osat.event.DatabaseCreatedEvent;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TenantAppServiceImpl implements TenantAppService {

    @Autowired
    private TenantDomainService tenantDomainService;

    @Autowired
    TenantAppConverter tenantAppConverter;

    @Override
    public TenantResponseDTO queryTenantInfo(Long id) {
        TenantId tenantId = new TenantId(id);
        Tenant tenant = tenantDomainService.queryTenantInfo(tenantId);

        return tenantAppConverter.toTenantResponseDTO(tenant);
    }

    @Override
    @DistributedLock(
            key = "'bwg_ops_locker_' + #tenantRequestDTO.getLicense().getType() + '_' + #tenantRequestDTO.getLicense().getIdentifier()",
            transType = "locker_createTenant_",
            timeoutSecond = 30
    )
    public TenantResponseDTO createTenant(TenantRequestDTO tenantRequestDTO) {

        //DTO 生成领域模型 Tenant
        Tenant tenant = tenantAppConverter.toTenant(tenantRequestDTO);
        //检测证件号是否存在
        tenantDomainService.checkTenantLicenseExist(tenant.getLicense(), tenant.getTenantId());

        //获取登录手机号的 user id
        UserId userId = tenantDomainService.createUserId(tenant.getLogin().getPhone());
        tenant.setUserId(userId);

        //调用 领域service 保存
        Tenant newTenant = tenantDomainService.createTenant(tenant);

        //超管员工信息创建  调用 管理服务 模块
        tenantDomainService.loginPhoneCreate(newTenant);

        //把保存返回的 生成领域模型 转换为 DTO 返回
        return tenantAppConverter.toTenantResponseDTO(newTenant);
    }

    @Override
    @DistributedLock(
            key = "'bwg_ops_locker_tenant_' + #tenantId",
            transType = "locker_editTenant_",
            timeoutSecond = 30
    )
    public TenantResponseDTO editTenant(TenantRequestDTO tenantRequestDTO, Long tenantId) {
        //DTO 生成领域模型 Tenant
        Tenant tenant = tenantAppConverter.toTenant(tenantRequestDTO);
        TenantId tenantIdObj = new TenantId(tenantId);
        tenant.setTenantId(tenantIdObj);

        //检测证件号是否存在
        tenantDomainService.checkTenantLicenseExist(tenant.getLicense(), tenantIdObj);

        Tenant currTenant = tenantDomainService.queryTenantInfo(tenantIdObj);

        //判断手机号是否发生变化，如果发生变化需要重新调用创建超管用户，
        // 并取消原来手机号的超管用户权限 依赖用户服务API
        boolean LoginPhoneChange = !currTenant.isEqualsLoginPhone(tenant.getLogin().getPhone().getValue());
        if (LoginPhoneChange || !currTenant.isHasUserId()) {
            UserId userId = tenantDomainService.getUserId(tenant.getLogin().getPhone());
            if (userId == null) {
                userId = tenantDomainService.createUserId(tenant.getLogin().getPhone());
            }
            tenant.setUserId(userId);
        }

        //判断开通状态，开通状态只允许修改超管手机号
        Tenant newTenant;
        if (currTenant.isActivated()) {
            //运营商激活状态只能修改超管手机号与超管姓名
            if (currTenant.isChangeBaseInfo(tenant)) {
                throw new AppException(ResponseCode.TENANT_NOT_ALLOWED_UPDATE_EXCEPT_SUPER);
            }
            newTenant = tenantDomainService.updateLogin(
                    tenantIdObj,
                    tenant.getLogin(),
                    tenant.getUserId()
            );
        } else {
            //调用 领域service 保存
            newTenant = tenantDomainService.updateTenant(tenant);
        }

        if (LoginPhoneChange) {
            //超管员工信息创建，如果超管手机号改变了
            tenantDomainService.loginPhoneChange(
                    newTenant,
                    currTenant
            );
        }

        return tenantAppConverter.toTenantResponseDTO(newTenant);
    }

    @Override
    public TenantListResponseDTO getTenantList(Integer currentPage, Integer pageSize, String keywords) {

        Long total = 0L;
        List<Tenant> tenantList;
        if ("".equals(keywords)) {
            tenantList = tenantDomainService.getTenantList(currentPage, pageSize);
            total = tenantDomainService.countTenantList();
        } else {
            tenantList = tenantDomainService.getTenantList(currentPage, pageSize, keywords);
            total = tenantDomainService.countTenantList(keywords);
        }
        List<TenantResponseDTO> tenantResponseDTOList = tenantAppConverter.toTenantResponseDTOList(tenantList);

        return tenantAppConverter.bulidTenantListResponseDTO(
                currentPage,
                pageSize,
                total,
                tenantResponseDTOList
        );
    }

    @Override
    public UserTenantListResponseDTO getUserTenantList(Long userId) {
//        List<Tenant> tenantList = tenantDomainService.getTenantListByUserId(UserId.of(userId));

        List<TenantStaff> tenantStaffList = tenantDomainService.queryTenantStaffByUserId(UserId.of(userId));
        List<Long> tenantIdList = new ArrayList<>();
        for (TenantStaff tenantStaff : tenantStaffList) {
            tenantIdList.add(tenantStaff.getTenantId().getValue());
        }

        List<Tenant> tenantList = tenantDomainService.getTenantListByTenantIdOrUserId(tenantIdList, UserId.of(userId));

        return tenantAppConverter.buildUserTenantListResponseDTO(userId, tenantList, tenantStaffList);
    }

    /**
     * 运营商开通/关停
     * @param tenantId
     * @param tenantServiceRequestDTO
     */
    @Override
    public void changeServiceStatus(Long tenantId, TenantServiceRequestDTO tenantServiceRequestDTO) {
        TenantStatus tenantStatus = new TenantStatus(tenantServiceRequestDTO.getStatus(), "");
        TenantId tenantIdObj = new TenantId(tenantId);

        if (tenantStatus.isActivate()) {
            //判断是否符合开通要求
            tenantDomainService.checkIsCanActivate(tenantIdObj);
            tenantDomainService.activatedServiceStatus(tenantIdObj);
        } else {
            //判断是否符合关闭要求
            tenantDomainService.checkIsCanClose(tenantIdObj);
            tenantDomainService.closedServiceStatus(tenantIdObj);
        }
    }
//
//    @Override
//    public void databaseCreatedCompleted(DatabaseCreatedEvent event) {
//        TenantStatus tenantStatus = new TenantStatus();
//        if (event.isRes()) {
//            tenantStatus.flowToActivated();
//        } else {
//            tenantStatus.flowToFailed();
//        }
//
//        tenantDomainService.updateTenantStatus(
//                TenantId.of(Long.valueOf(event.getTenantId())),
//                tenantStatus
//        );
//
//        //给超管发送已开通短信
//        if (tenantStatus.isActivate()) {
//            tenantDomainService.sendActivatedServiceSms(TenantId.of(Long.valueOf(event.getTenantId())));
//        }
//    }

    /**
     * 运营商员工信息创建/变更事件处理
     *
     * @param staffCreateEventDTO
     */
    @Override
    @Transactional
    public void tenantStaffChanged(StaffCreateEvent staffCreateEventDTO) {
        //DTO 转换为 领域模型 TenantStaff
       TenantStaff tenantStaff = tenantAppConverter.toTenantStaff(staffCreateEventDTO);

        tenantDomainService.saveTenantStaff(tenantStaff);

        if (staffCreateEventDTO.isHasOldUserId() && staffCreateEventDTO.isUserIdChange()) {
            //软删除原来的
            tenantDomainService.deleteTenantStaff(
                    UserId.of(staffCreateEventDTO.getOldUserId()),
                    TenantId.of(staffCreateEventDTO.getTenantId())
            );
        }
    }
}
