package com.inboyu.operation.app.controller;

import com.inboyu.operation.app.application.PermissionAppService;
import com.inboyu.operation.app.dto.response.permission.MenuPermissionResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController

@RequestMapping("/api/v1/permissions")
@Tag(name = "菜单与权限定义")
public class PermissionController {

    @Autowired
    PermissionAppService permissionAppService;

    @GetMapping
    @Operation(summary = "菜单与权限定义树")
    public MenuPermissionResponseDTO getMenuPermissionTree() {
        return permissionAppService.getMenuPermissionTree();
    }

}
