package com.inboyu.operation.app.controller;

import com.inboyu.operation.app.application.TenantAppService;
import com.inboyu.operation.app.dto.response.user.UserTenantListResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "用户运营商")
public class UserController {

    @Autowired
    private TenantAppService tenantAppService;

    /**
     * 获取指定用户的运营商列表
     *
     * @return
     */
    @GetMapping("{userId}/tenants")
    @Operation(summary = "获取用户关联的运营商")
    public UserTenantListResponseDTO getUserTenantList(
            @Schema(description = "用户ID", example = "0") @PathVariable(value = "userId") @NotNull(message = "用户ID允许为空") Long userId
    ) {
        return tenantAppService.getUserTenantList(userId);
    }
}
