package com.inboyu.operation.app.mq;

import com.inboyu.admin.constant.AdminRocketMqTopicConstant;
import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.alimq.annotation.RocketMQTag;
import com.inboyu.alimq.annotation.RocketMQTopic;
import com.inboyu.operation.app.application.TenantAppService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 运营商员工事件监听
 */
@RocketMQTopic(topic = AdminRocketMqTopicConstant.TOPIC_BWG_TENANT)
public class TenantStaffEventListener {

    @Autowired
    TenantAppService tenantAppService;

    /**
     * 运营商员工创建/更新事件
     * @param message
     */
    @RocketMQTag(tag = AdminRocketMqTopicConstant.TAG_STAFF_CREATED)
    public void staffCreate(StaffCreateEvent message) {
        tenantAppService.tenantStaffChanged(message);
    }
}
