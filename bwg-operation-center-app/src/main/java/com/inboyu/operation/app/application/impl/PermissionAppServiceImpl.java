package com.inboyu.operation.app.application.impl;

import com.inboyu.operation.app.application.PermissionAppService;
import com.inboyu.operation.app.dto.converter.PermissionAppConverter;
import com.inboyu.operation.app.dto.response.permission.MenuPermissionResponseDTO;
import com.inboyu.operation.domain.permission.model.PermissionGroup;
import com.inboyu.operation.domain.permission.model.PermissionScope;
import com.inboyu.operation.domain.permission.service.PermissionDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermissionAppServiceImpl implements PermissionAppService {

    @Autowired
    PermissionDomainService permissionDomainService;

    @Autowired
    PermissionAppConverter permissionAppConverter;

    @Override
    public MenuPermissionResponseDTO getMenuPermissionTree() {
        List<PermissionGroup> permissionGroupList =  permissionDomainService.getPermissionGroupList();
        List<PermissionScope> permissionScopeList =  permissionDomainService.getPermissionScopeList();

        //处理成 tree 结构并转换为 MenuPermissionResponseDTO 对象
        return permissionAppConverter.toMenuPermissionResponseDTO(permissionGroupList, permissionScopeList);
    }
}
