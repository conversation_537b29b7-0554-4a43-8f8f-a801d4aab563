package com.inboyu.operation.app.dto.response.tenant;

import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "运营商分页查询结果")
public class TenantListResponseDTO extends Pagination<TenantResponseDTO> {

    public TenantListResponseDTO(int pageNum, int pageSize, int totalPages, long total, List<TenantResponseDTO> list) {
        super(pageNum, pageSize, totalPages, total, list);
    }
}
