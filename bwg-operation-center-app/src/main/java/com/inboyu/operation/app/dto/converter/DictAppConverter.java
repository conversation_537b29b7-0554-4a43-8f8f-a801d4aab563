package com.inboyu.operation.app.dto.converter;

import com.inboyu.operation.domain.dict.model.Dict;
import com.inboyu.operation.dto.response.DictListResponseDTO;
import com.inboyu.util.convert.PojoConvertUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class DictAppConverter {

    public DictListResponseDTO.DictItemDTO toDictItemDTO(Dict dict) {
        DictListResponseDTO.DictItemDTO dictItemDTO = PojoConvertUtil.convert(dict, DictListResponseDTO.DictItemDTO.class);
        dictItemDTO.setCode(dict.getCode().getValue());

        return dictItemDTO;
    }

    public DictListResponseDTO toDictListResponseDTO(List<Dict> dictList) {
        DictListResponseDTO dictListResponseDTO = new DictListResponseDTO();
        List<DictListResponseDTO.DictItemDTO> dictItemDTOList =  dictList
                .stream()
                .map(this::toDictItemDTO)
                .filter(Objects::nonNull)
                .toList();
        dictListResponseDTO.setDict(dictItemDTOList);

        return dictListResponseDTO;
    }

}
