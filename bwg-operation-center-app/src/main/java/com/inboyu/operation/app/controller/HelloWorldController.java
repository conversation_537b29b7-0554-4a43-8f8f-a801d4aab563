package com.inboyu.operation.app.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025年07月22日 16:29
 */
@RestController
@RequestMapping("example")
public class HelloWorldController {
//    @Autowired
//    private HelloBiz helloBiz;
//
//    @Autowired
//    private WorldBiz worldBiz;

//    @PostMapping("helloWorld")
//    public HelloWorldResponse helloWorld(@RequestBody @Valid HelloWorldRequest request) {
//        String hello = helloBiz.hello();
//        String world = worldBiz.world();
//        HelloWorldResponse response = new HelloWorldResponse();
//        response.setData(hello + world);
//        return response;
//    }
}
