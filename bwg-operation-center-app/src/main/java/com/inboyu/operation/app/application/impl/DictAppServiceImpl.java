package com.inboyu.operation.app.application.impl;

import com.inboyu.operation.app.application.DictAppService;
import com.inboyu.operation.app.dto.converter.DictAppConverter;
import com.inboyu.operation.domain.dict.model.Dict;
import com.inboyu.operation.domain.dict.repository.DictRepository;
import com.inboyu.operation.domain.dict.service.DictDomainService;
import com.inboyu.operation.dto.response.DictListResponseDTO;
import com.inboyu.operation.dto.response.DictListResponseDTO.DictItemDTO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DictAppServiceImpl implements DictAppService {

    @Autowired
    DictDomainService dictDomainService;

    @Autowired
    DictAppConverter dictAppConverter;

    @Autowired
    private DictRepository dictRepository;

    @Override
    public DictListResponseDTO getDictList(String type) {
        List<Dict> dictList = dictDomainService.getDictList(type);

        return dictAppConverter.toDictListResponseDTO(dictList);
    }

    @Override
    public Map<String, List<DictItemDTO>> groupByTypes(List<String> types) {
        List<Dict> dicts = dictRepository.findByTypes(types);
        return dicts.stream()
                .collect(Collectors.groupingBy(
                        dict -> dict.getType().getValue(), // 分组依据：type的值
                        Collectors.mapping( // 转换每个Dict为DictItemDTO
                                dict -> dictAppConverter.toDictItemDTO(dict),
                                Collectors.toList() // 收集为List<DictItemDTO>
                        )));
    }
}
