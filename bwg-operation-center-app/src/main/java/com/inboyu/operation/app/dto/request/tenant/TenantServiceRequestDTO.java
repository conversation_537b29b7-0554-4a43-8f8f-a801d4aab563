package com.inboyu.operation.app.dto.request.tenant;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "运营商服务开通")
public class TenantServiceRequestDTO {

    /**
     * 运营商名称
     */
    @Schema(description = "状态", example = "tenant_status.activated")
    @NotBlank(message = "运营商服务状态不能为空")
    private String status;
}
