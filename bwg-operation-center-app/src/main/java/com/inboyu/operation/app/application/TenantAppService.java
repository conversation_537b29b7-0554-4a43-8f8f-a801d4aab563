package com.inboyu.operation.app.application;

import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.operation.app.dto.request.tenant.TenantRequestDTO;
import com.inboyu.operation.app.dto.request.tenant.TenantServiceRequestDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantListResponseDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantResponseDTO;
import com.inboyu.operation.app.dto.response.user.UserTenantListResponseDTO;
//import com.inboyu.osat.event.DatabaseCreatedEvent;

public interface TenantAppService {
    TenantResponseDTO queryTenantInfo(Long tenantId);
    TenantResponseDTO createTenant(TenantRequestDTO tenantRequestDTO);
    TenantResponseDTO editTenant(TenantRequestDTO tenantRequestDTO, Long tenantId);

    TenantListResponseDTO getTenantList(Integer currentPage, Integer pageSize, String keywords);

    UserTenantListResponseDTO getUserTenantList(Long userId);

    void changeServiceStatus(Long tenantId, TenantServiceRequestDTO tenantServiceDTO);

//    void databaseCreatedCompleted(DatabaseCreatedEvent databaseCreatedEvent);

    void tenantStaffChanged(StaffCreateEvent staffCreateEventDTO);
}
