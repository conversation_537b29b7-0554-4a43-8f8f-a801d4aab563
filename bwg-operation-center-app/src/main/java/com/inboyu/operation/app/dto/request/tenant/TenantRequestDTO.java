package com.inboyu.operation.app.dto.request.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "运营商信息")
public class TenantRequestDTO {

    /**
     * 运营商名称
     */
    @Schema(description = "运营商名称", example = "示例科技有限公司")
    @NotBlank(message = "运营商名称不能为空")
    private String title;

    /**
     * 证件信息
     */
    @Schema(description = "证件信息")
    @NotNull(message = "证件信息不能为空")
    private License license;

    /**
     * 联系人信息
     */
    @Schema(description = "联系人信息")
    @NotNull(message = "联系信息不能为空")
    private Contact contact;

    /**
     * 登录手机号
     */
    @Schema(description = "超管姓名", example = "李四")
    @NotBlank(message = "超管姓名不能为空")
    private String loginName;

    /**
     * 登录手机号
     */
    @Schema(description = "登录手机号", example = "13800138000")
    @NotBlank(message = "登录手机号不能为空")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    private String loginPhone;

    /**
     * 备注
     */
    @Schema(description = "备注信息", example = "优质客户")
    private String remark;
//
//    @AssertTrue(message = "身份证格式不正确")
//    public boolean isIdNumberValid() {
//        if ("大陆身份证".equals(license.type)) {
//            return license.identifier != null && license.identifier.matches("^\\d{17}[0-9X]$");
//        }
//        return true;
//    }
//
//    @AssertTrue(message = "统一社会信用代码格式不正确")
//    public boolean isIdCreditCodeValid() {
//        if ("统一社会信用代码".equals(license.type)) {
//
//        }
//        return true;
//    }
//
//    @AssertTrue(message = "港澳台通行证格式不正确")
//    public boolean isIdHMDIdValid() {
//        if ("港澳台通行证".equals(license.type)) {
//
//        }
//        return true;
//    }
//
//    @AssertTrue(message = "护照格式不正确")
//    public boolean isIdPassportIdValid() {
//        if ("护照".equals(license.type)) {
//
//        }
//        return true;
//    }
//
//    @AssertTrue(message = "军官证格式不正确")
//    public boolean isIdOfficerIdValid() {
//        if ("军官证".equals(license.type)) {
//
//        }
//        return true;
//    }

    /**
     * 证件信息子对象
     */
    @Data
    @Schema(description = "证件信息")
    public static class License {
        /**
         * 证件类型
         */
        @Schema(description = "证件类型-字典表维护", example = "license_type.id_card_no")
        @NotBlank(message = "证件类型不能为空")
        private String type;

        /**
         * 证件号码
         */
        @Schema(description = "证件号码", example = "110101199001011234")
        @NotBlank(message = "证件号不能为空")
        private String identifier;
    }

    /**
     * 联系人信息子对象
     */
    @Data
    @Schema(description = "联系人信息")
    public static class Contact {
        /**
         * 联系人姓名
         */
        @Schema(description = "联系人姓名", example = "张三")
        @NotBlank(message = "联系人名称必填")
        private String name;

        /**
         * 联系人手机号
         */
        @Schema(description = "联系人手机号", example = "13900139000")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式有误")
        private String phone;
    }
}
