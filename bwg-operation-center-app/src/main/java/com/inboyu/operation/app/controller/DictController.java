package com.inboyu.operation.app.controller;

import com.inboyu.operation.api.DictFeignClient;
import com.inboyu.operation.app.application.DictAppService;
import com.inboyu.operation.dto.response.DictListResponseDTO;
import com.inboyu.operation.dto.response.DictListResponseDTO.DictItemDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/dict")
@Tag(name = "系统字典")
public class DictController implements DictFeignClient {

    @Autowired
    private DictAppService dictAppService;

    @GetMapping
    @Operation(summary = "获取字典项列表")
    public DictListResponseDTO getDictList(
            @Schema(description = "字典类型", example = "license_type") @RequestParam(value = "type", required = false) String type) {
        return dictAppService.getDictList(type);
    }

    @GetMapping("/group")
    @Operation(summary = "根据类型进行分组")
    public Map<String,  List<DictItemDTO>> groupByTypes(
            @Schema(description = "字典类型", example = "license_type") @RequestParam(value = "type", required = false) String[] type) {
        return dictAppService.groupByTypes(new ArrayList<>(List.of(type)));
    }

}
