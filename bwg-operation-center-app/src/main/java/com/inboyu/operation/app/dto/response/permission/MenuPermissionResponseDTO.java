package com.inboyu.operation.app.dto.response.permission;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 菜单权限结构DTO
 * 用于封装多级菜单及关联的权限信息
 */
@Data
@Schema(description = "菜单权限结构数据")
public class MenuPermissionResponseDTO {

    /**
     * 菜单列表（一级菜单）
     */
    @Schema(description = "一级菜单集合")
    private List<MenuDTO> menus;

    /**
     * 菜单DTO（支持三级嵌套）
     */
    @Data
    @Schema(description = "菜单信息（支持三级嵌套）")
    public static class MenuDTO {

        /**
         * 菜单编码
         */
        @Schema(description = "菜单唯一编码", example = "system_manage")
        private String code;

        /**
         * 菜单名称
         */
        @Schema(description = "菜单显示名称", example = "系统管理")
        private String title;

        /**
         * 菜单图标
         */
        @Schema(description = "菜单显示图标", example = "setting", nullable = true)
        private String icon;

        /**
         * 菜单关联的权限集合
         */
        @Schema(description = "当前菜单包含的权限列表", nullable = true)
        private List<PermissionDTO> permissions;

        /**
         * 子菜单（二级菜单）
         */
        @Schema(description = "二级子菜单列表，最多嵌套2层", nullable = true)
        private List<MenuDTO> children;
    }

    /**
     * 权限DTO
     */
    @Data
    @Schema(description = "权限信息")
    public static class PermissionDTO {

        /**
         * 权限编码
         */
        @Schema(description = "权限唯一编码", example = "user_add")
        private String code;

        /**
         * 权限名称
         */
        @Schema(description = "权限显示名称", example = "用户新增")
        private String title;

        /**
         * 接口URL
         */
        @Schema(description = "权限对应的接口地址", example = "/api/v1/users", nullable = true)
        private String url;
    }
}
