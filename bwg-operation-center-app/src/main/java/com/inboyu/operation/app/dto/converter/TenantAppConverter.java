package com.inboyu.operation.app.dto.converter;

import com.inboyu.admin.event.StaffCreateEvent;
import com.inboyu.operation.app.dto.request.tenant.TenantRequestDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantListResponseDTO;
import com.inboyu.operation.app.dto.response.tenant.TenantResponseDTO;
import com.inboyu.operation.app.dto.response.user.UserTenantListResponseDTO;
import com.inboyu.operation.domain.tenant.model.*;
import com.inboyu.util.convert.PojoConvertUtil;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class TenantAppConverter {

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public TenantResponseDTO toTenantResponseDTO(Tenant tenant) {
        TenantResponseDTO tenantResponseDTO = PojoConvertUtil.convert(tenant, TenantResponseDTO.class);

        tenantResponseDTO.setTenantId(tenant.getTenantId().getValue().toString());
        tenantResponseDTO.setTitle(tenant.getTitle());
        TenantResponseDTO.License license = new TenantResponseDTO.License();

        TenantResponseDTO.LicenseType licenseType = new TenantResponseDTO.LicenseType();
        licenseType.setCode(tenant.getLicense().getType().getCode());
        licenseType.setTitle(tenant.getLicense().getType().getTitle());
        license.setType(licenseType);
        license.setIdentifier(tenant.getLicense().getIdentifier());
        tenantResponseDTO.setLicense(license);

        TenantResponseDTO.Contact contact = new TenantResponseDTO.Contact();
        contact.setName(tenant.getContact().getName());
        contact.setPhone(tenant.getContact().getPhone().getValue());
        tenantResponseDTO.setContact(contact);

        TenantResponseDTO.Admin admin = new TenantResponseDTO.Admin();
        admin.setUserId(tenant.getUserId().getValue().toString());
        admin.setPhone(tenant.getLogin().getPhone().getValue());
        admin.setName(tenant.getLogin().getName());
        tenantResponseDTO.setAdmin(admin);

        TenantResponseDTO.Status status = new TenantResponseDTO.Status();
        status.setCode(tenant.getStatus().getCode());
        status.setTitle(tenant.getStatus().getValue());
        tenantResponseDTO.setStatus(status);
        tenantResponseDTO.setCreateTime(tenant.getCreateTime().format(formatter));

        return tenantResponseDTO;
    }

    public Tenant toTenant(TenantRequestDTO tenantRequestDTO) {
        Tenant tenant = PojoConvertUtil.convert(tenantRequestDTO, Tenant.class);
        tenant.setContact(new Contact(
                tenantRequestDTO.getContact().getName(),
                new PhoneNumber((tenantRequestDTO.getContact().getPhone())
                )));
        tenant.setLicense(new License(
                new LicenseType(tenantRequestDTO.getLicense().getType(), ""),
                tenantRequestDTO.getLicense().getIdentifier()
        ));
        Login login = new Login();
        login.setName(tenantRequestDTO.getLoginName());
        login.setPhone(new PhoneNumber(tenantRequestDTO.getLoginPhone()));
        tenant.setLogin(login);

        return tenant;
    }

    public List<TenantResponseDTO> toTenantResponseDTOList(List<Tenant> tenantList) {
        List<TenantResponseDTO> tenantResponseDTOList = new ArrayList<TenantResponseDTO>();
        for(Tenant tenant : tenantList) {
            tenantResponseDTOList.add(toTenantResponseDTO(tenant));
        }

        return tenantResponseDTOList;
    }

    public TenantListResponseDTO bulidTenantListResponseDTO(
            Integer currentPage,
            Integer pageSize,
            Long total,
            List<TenantResponseDTO> tenantResponseDTOList
    ) {
        int totalPages = (total.intValue() + pageSize - 1) / pageSize;

        return new TenantListResponseDTO(
                currentPage,
                pageSize,
                totalPages,
                total,
                tenantResponseDTOList
        );
    }

    public UserTenantListResponseDTO buildUserTenantListResponseDTO(
            Long userId,
            List<Tenant> tenantList,
            List<TenantStaff> tenantStaffList
    ) {

        HashMap<Long, TenantStaff> tenantStaffMap = new HashMap<>();
        for (TenantStaff tenantStaff : tenantStaffList) {
            tenantStaffMap.put(tenantStaff.getTenantId().getValue(), tenantStaff);
        }

        UserTenantListResponseDTO userTenantListResponseDTO = new UserTenantListResponseDTO();
        List<UserTenantListResponseDTO.TenantSimpleDTO> tenants = new ArrayList<>();
        for (Tenant tenant : tenantList) {
            UserTenantListResponseDTO.TenantSimpleDTO tenantSimpleDTO = new  UserTenantListResponseDTO.TenantSimpleDTO();
            tenantSimpleDTO.setTenantId(tenant.getTenantId().getValue().toString());
            tenantSimpleDTO.setTitle(tenant.getTitle());
            if (tenantStaffMap.containsKey(tenant.getTenantId().getValue())) {
                TenantStaff tenantStaff = tenantStaffMap.get(tenant.getTenantId().getValue());
                tenantSimpleDTO.setStaffId(tenantStaff.getStaffId().getValue().toString());
                 //判断staffStatus
                tenantSimpleDTO.setStaffStatus(tenantStaff.getStatus().getCode().equals("status.enabled"));
            }

            tenantSimpleDTO.setIsAdmin(false);
            if (userId.equals(tenant.getUserId().getValue() )) {
                tenantSimpleDTO.setIsAdmin(true);
                
            }
            tenantSimpleDTO.setAdminName(tenant.getLogin().getName());
           

            tenants.add(tenantSimpleDTO);
        }
        userTenantListResponseDTO.setTenants(tenants);

        return userTenantListResponseDTO;
    }

    /**
     * 把员工创建/变更事件 DTO 转为 领域模型
     * @param staffCreateEventDTO
     * @return
     */
    public TenantStaff toTenantStaff(StaffCreateEvent staffCreateEventDTO) {
        TenantStaff tenantStaff = new TenantStaff();
        tenantStaff.setTenantId(TenantId.of(Long.valueOf(staffCreateEventDTO.getTenantId())));
        tenantStaff.setStaffId(StaffId.of(Long.valueOf(staffCreateEventDTO.getStaffId())));
        tenantStaff.setUserId(UserId.of(Long.valueOf(staffCreateEventDTO.getUserId())));
        tenantStaff.setStatus(new StaffStatus(staffCreateEventDTO.getStatus(), ""));

        return tenantStaff;
    }
}
