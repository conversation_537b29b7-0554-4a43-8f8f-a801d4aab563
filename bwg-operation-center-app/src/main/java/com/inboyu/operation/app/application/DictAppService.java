package com.inboyu.operation.app.application;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.inboyu.operation.dto.response.DictListResponseDTO;
import com.inboyu.operation.dto.response.DictListResponseDTO.DictItemDTO;

/**
 * 系统字段
 */
@Service
public interface DictAppService {
    public DictListResponseDTO getDictList(String type);

    /**
     * 根据类型分组字典
     * @param type
     * @return
     */
    public Map<String, List<DictItemDTO>> groupByTypes(List<String> types);
}
