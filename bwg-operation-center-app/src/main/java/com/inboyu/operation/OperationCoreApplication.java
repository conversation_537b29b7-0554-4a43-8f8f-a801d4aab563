package com.inboyu.operation;

import com.inboyu.alimq.annotation.EnableAliMQ;
import com.inboyu.spring.cloud.starter.web.EnableWeb;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableFeignClients(basePackages = "com.inboyu")
@ComponentScan(basePackages = {"com.inboyu"})
@SpringBootApplication
@EnableWeb
@EnableAliMQ
public class OperationCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(OperationCoreApplication.class, args);
    }

}
