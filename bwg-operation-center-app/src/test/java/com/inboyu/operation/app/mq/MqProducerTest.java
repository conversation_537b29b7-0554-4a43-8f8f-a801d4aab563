package com.inboyu.operation.app.mq;

import com.aliyun.openservices.ons.api.SendResult;
import com.inboyu.alimq.producer.RocketMQTemplate;
import com.inboyu.operation.event.LoginPhoneChangeEvent;
import com.inboyu.operation.event.TenantCreatedEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MqProducerTest {

    @Autowired
    RocketMQTemplate rocketMQTemplate;

    @Test
    public void sendLoginPhoneChangeEvent() {
        LoginPhoneChangeEvent loginPhoneChangeEvent = new LoginPhoneChangeEvent();
        loginPhoneChangeEvent.setNewPhone("13800138000");
        loginPhoneChangeEvent.setNewName("李四");
        loginPhoneChangeEvent.setNewUserId(1L);
        loginPhoneChangeEvent.setOldPhone("13800138001");
        loginPhoneChangeEvent.setNewUserId(0L);
        SendResult result = rocketMQTemplate.send(loginPhoneChangeEvent);

        System.err.println(result);
    }

    @Test
    public void sendTenantChangeEvent() {
        TenantCreatedEvent tenantChangeEvent = new TenantCreatedEvent();
        tenantChangeEvent.setTenantId("341518448242855936");

        SendResult result = rocketMQTemplate.send(tenantChangeEvent);

        System.err.println(result);
    }

}
