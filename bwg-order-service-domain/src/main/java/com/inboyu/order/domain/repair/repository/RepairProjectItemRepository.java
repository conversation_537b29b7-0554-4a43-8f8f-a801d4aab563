package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairStoreItem;
import com.inboyu.order.domain.repair.model.ProjectId;

import java.util.List;
import java.util.Optional;

/**
 * 项目物品配置仓储接口
 */
public interface RepairProjectItemRepository {

    /**
     * 根据配置ID查询
     */
    Optional<RepairStoreItem> findById(Long configId);

    /**
     * 根据项目ID查询配置列表
     */
    List<RepairStoreItem> findByProjectId(ProjectId projectId);

    /**
     * 根据物品ID查询配置列表
     */
    List<RepairStoreItem> findByItemId(Long itemId);

    /**
     * 根据项目ID和物品ID查询配置
     */
    Optional<RepairStoreItem> findByProjectIdAndItemId(ProjectId projectId, Long itemId);

    /**
     * 根据排序权重范围查询配置列表
     */
    List<RepairStoreItem> findBySortBetween(Integer minSort, Integer maxSort);

    /**
     * 根据项目ID和排序权重范围查询配置列表
     */
    List<RepairStoreItem> findByProjectIdAndSortBetween(ProjectId projectId, Integer minSort, Integer maxSort);

    /**
     * 根据项目ID查询配置列表（按排序权重排序）
     */
    List<RepairStoreItem> findByProjectIdOrderBySort(ProjectId projectId);

    /**
     * 分页查询配置记录
     */
    List<RepairStoreItem> findByProjectIdAndFilters(ProjectId projectId, String filterBy, String filter,
                                                    Integer pageNum, Integer pageSize);

    /**
     * 统计配置数量
     */
    long countAll();

    /**
     * 统计指定项目的配置数量
     */
    long countByProjectId(ProjectId projectId);

    /**
     * 统计指定物品的配置数量
     */
    long countByItemId(Long itemId);

    /**
     * 保存配置
     */
    RepairStoreItem save(RepairStoreItem repairStoreItem);

    /**
     * 批量保存配置
     */
    List<RepairStoreItem> saveAll(List<RepairStoreItem> repairStoreItems);

    /**
     * 更新配置
     */
    RepairStoreItem update(RepairStoreItem repairStoreItem);

    /**
     * 根据ID删除配置
     */
    void deleteById(Long configId);

    /**
     * 根据项目ID删除所有配置
     */
    void deleteByProjectId(ProjectId projectId);

    /**
     * 根据物品ID删除所有配置
     */
    void deleteByItemId(Long itemId);

    /**
     * 根据项目ID和物品ID删除配置
     */
    void deleteByProjectIdAndItemId(ProjectId projectId, Long itemId);

    /**
     * 更新排序权重
     */
    void updateSortById(Long configId, Integer sort);

    /**
     * 批量更新排序权重
     */
    void updateSortByIds(List<Long> configIds, List<Integer> sorts);

    /**
     * 重新排序项目物品配置
     */
    void reorderByProjectId(ProjectId projectId, List<Long> itemIds);
}
