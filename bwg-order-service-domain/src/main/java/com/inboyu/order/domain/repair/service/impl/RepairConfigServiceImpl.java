package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.StoreId;
import com.inboyu.order.domain.repair.repository.RepairConfigRepository;
import com.inboyu.order.domain.repair.service.RepairConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.Optional;

/**
 * 报修配置领域服务实现
 */
@Slf4j
@Service
public class RepairConfigServiceImpl implements RepairConfigService {


}
