package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报修工单领域模型（聚合根）
 */
@Data
@Builder
public class RepairOrder {
    private Long id; // 自增ID，无业务含义
    private LocalDateTime createTime; // 记录创建时间
    private LocalDateTime modifyTime; // 记录修改时间
    private Long version; // 记录版本号（乐观锁）
    private Boolean deleted; // 是否删除，0否，1是
    
    // 使用值对象
    private RepairOrderId repairOrderId; // 报修工单业务ID
    private CustomerId customerId; // 客户ID
    private RoomId roomId; // 房间ID
    private Long buildingId; // 楼栋ID
    private Long itemId; // 报修物品ID
    private WorkflowId workflowId; // 工作流ID
    
    // 业务字段
    private String area; // 报修区域
    private String detail; // 问题描述
    private String contactPhone; // 联系电话，加密存储
    private String submitter; // 报修人姓名
    private LocalDate willRepairDate; // 意向维修日期
    private Integer willRepairStart; // 意向维修开始时间段
    private Integer willRepairEnd; // 意向维修结束时间段
    private Boolean isEnter; // 是否无人可直接入户
    
    // 使用值对象
    private RepairOrderStatus status; // 报修状态
    private OrderType orderType; // 工单类型
    private TagStatus tagStatus; // 标签状态
    
    // 业务字段
    private String refuseReason; // 拒绝理由
    private String suspendReason; // 挂起原因
    private LocalDateTime preDoorTime; // 预计上门时间
    private LocalDateTime preRepairTime; // 预计完成时间
    private LocalDateTime finishTime; // 完成时间
    private LocalDateTime dealTime; // 受理时间
    private Long responsibleId; // 责任方ID
    private Long maintainerId; // 处理人ID
    private String remark; // 备注
    
    /**
     * 创建报修工单
     */
    public static RepairOrder create(CustomerId customerId, RoomId roomId, 
                                   Long itemId, String area, String detail, String contactPhone, 
                                   String submitter, LocalDate willRepairDate, Integer willRepairStart, 
                                   Integer willRepairEnd, Boolean isEnter) {
        return RepairOrder.builder()
                .repairOrderId(RepairOrderId.generate()) // 生成新的工单ID
                .customerId(customerId)
                .roomId(roomId)
                .itemId(itemId)
                .area(area)
                .detail(detail)
                .contactPhone(contactPhone)
                .submitter(submitter)
                .willRepairDate(willRepairDate)
                .willRepairStart(willRepairStart)
                .willRepairEnd(willRepairEnd)
                .isEnter(isEnter)
                .status(RepairOrderStatus.SUBMITTED)
                .orderType(OrderType.OCCUPIED)
                .tagStatus(TagStatus.NORMAL)
                .createTime(LocalDateTime.now())
                .deleted(false)
                .version(0L)
                .build();
    }
    
    /**
     * 受理报修工单
     */
    public void accept(Long userId, String userName) {
        if (!status.equals(RepairOrderStatus.SUBMITTED)) {
            throw new IllegalStateException("只有已提交状态的工单才能被受理");
        }
        this.status = RepairOrderStatus.PROCESSING;
        this.maintainerId = userId;
        this.dealTime = LocalDateTime.now();
        this.modifyTime = LocalDateTime.now();
    }
    
    /**
     * 拒绝报修工单
     */
    public void reject(String reason) {
        if (!status.equals(RepairOrderStatus.SUBMITTED)) {
            throw new IllegalStateException("只有已提交状态的工单才能被拒绝");
        }
        this.status = RepairOrderStatus.CLOSED;
        this.refuseReason = reason;
        this.modifyTime = LocalDateTime.now();
    }
    
    /**
     * 完成报修工单
     */
    public void complete() {
        if (!status.equals(RepairOrderStatus.PROCESSING)) {
            throw new IllegalStateException("只有处理中状态的工单才能被完成");
        }
        this.status = RepairOrderStatus.COMPLETED;
        this.finishTime = LocalDateTime.now();
        this.modifyTime = LocalDateTime.now();
    }
    
    /**
     * 重开报修工单
     */
    public void reopen(String reason) {
        if (!status.equals(RepairOrderStatus.COMPLETED)) {
            throw new IllegalStateException("只有已完成状态的工单才能被重开");
        }
        this.status = RepairOrderStatus.PROCESSING;
        this.tagStatus = TagStatus.REWORK;
        this.remark = reason;
        this.modifyTime = LocalDateTime.now();
    }
    
    /**
     * 挂起报修工单
     */
    public void suspend(String reason) {
        if (!status.equals(RepairOrderStatus.PROCESSING)) {
            throw new IllegalStateException("只有处理中状态的工单才能被挂起");
        }
        this.tagStatus = TagStatus.SUSPENDED;
        this.suspendReason = reason;
        this.modifyTime = LocalDateTime.now();
    }
    
    /**
     * 转派报修工单
     */
    public void transfer(Long assigneeId, String assigneeName, String reason) {
        if (!status.equals(RepairOrderStatus.PROCESSING)) {
            throw new IllegalStateException("只有处理中状态的工单才能被转派");
        }
        this.maintainerId = assigneeId;
        this.remark = reason;
        this.modifyTime = LocalDateTime.now();
    }
}
